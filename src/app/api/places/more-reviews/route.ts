import { NextRequest, NextResponse } from "next/server";

// This endpoint is now just a pass-through to the Google Places API
// We're only using real data from the API, no synthetic reviews

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const placeId = searchParams.get("placeId");

  if (!placeId) {
    return NextResponse.json(
      { error: "placeId parameter is required" },
      { status: 400 }
    );
  }

  try {
    const apiKey = process.env.GOOGLE_MAPS_API_KEY;

    if (!apiKey) {
      return NextResponse.json(
        { error: "Google Maps API key is not configured" },
        { status: 500 }
      );
    }

    // Get place details with reviews directly from Google Places API
    // We're requesting all available fields to get as much data as possible
    const detailsUrl = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${placeId}&fields=name,rating,user_ratings_total,reviews&key=${apiKey}`;
    console.log(`Fetching reviews from Google Places API: ${detailsUrl}`);

    const detailsResponse = await fetch(detailsUrl);

    if (!detailsResponse.ok) {
      throw new Error(`Google Places API error: ${detailsResponse.statusText}`);
    }

    const detailsData = await detailsResponse.json();
    console.log(`Received response from Google Places API for ${placeId}`);

    // Extract reviews and metadata
    const reviews = detailsData.result.reviews || [];
    const totalReviews = detailsData.result.user_ratings_total || 0;

    console.log(
      `Found ${reviews.length} reviews out of ${totalReviews} total reviews`
    );

    // Return only the real reviews from the API
    return NextResponse.json({
      reviews: reviews,
      total_reviews: totalReviews,
      // There are no more reviews we can fetch from the API
      has_more: false,
    });
  } catch (error) {
    console.error("Error fetching reviews from Google Places API:", error);
    return NextResponse.json(
      { error: "Failed to fetch reviews from Google Places API" },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';

/**
 * API route to fetch reviews from Featurable API
 * This provides more reviews than the standard Google Places API
 */
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const locationId = searchParams.get('locationId');
  const accountId = searchParams.get('accountId');
  const nextPageToken = searchParams.get('nextPageToken');
  const pageSize = searchParams.get('pageSize') || '50';
  const minStars = searchParams.get('minStars') || '1'; // Get all reviews by default
  
  if (!locationId || !accountId) {
    return NextResponse.json(
      { error: 'locationId and accountId parameters are required' },
      { status: 400 }
    );
  }

  try {
    const apiKey = process.env.FEATURABLE_API_KEY;
    
    if (!apiKey) {
      return NextResponse.json(
        { error: 'Featurable API key is not configured' },
        { status: 500 }
      );
    }
    
    // Build the URL with query parameters
    let url = `https://featurable.com/api/v1/accounts/${accountId}/locations/${locationId}/reviews?apiKey=${apiKey}`;
    
    // Add optional parameters if provided
    if (nextPageToken) {
      url += `&nextPageToken=${encodeURIComponent(nextPageToken)}`;
    }
    
    url += `&pageSize=${pageSize}&minStars=${minStars}`;
    
    console.log(`Fetching reviews from Featurable API: ${url.replace(apiKey, 'API_KEY_HIDDEN')}`);
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Featurable API error: ${response.statusText}`);
    }
    
    const data = await response.json();
    console.log(`Received ${data.reviews?.length || 0} reviews from Featurable API`);
    
    // Return the data directly
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching reviews from Featurable API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reviews from Featurable API' },
      { status: 500 }
    );
  }
}

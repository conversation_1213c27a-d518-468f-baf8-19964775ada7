"use client";

import { useEffect, useState, useCallback } from "react";
import { getPlaceDetails, PlaceDetails } from "@/utils/googlePlaces";

import {
  saveUserReview,
  getUserReviews,
  UserReview as UserReviewType,
  saveUserComplaint,
  getUserComplaints,
  UserComplaint,
  SplitReview,
  saveSplitReview,
  getSplitReviews,
  voteForReview,
  voteForComplaint,
  voteForSplitReview,
  hasUserVoted,
  getTopReviews,
  getTopComplaints,
  getTopSplitGoodStuff,
  getTopSplitBadStuff,
  WIDGET_OPTIONS,
  MOOD_OPTIONS,
  VIBE_OPTIONS,
  COMPLAINT_CATEGORIES,
} from "@/utils/userReviews";
import SplitReviewForm from "@/components/SplitReviewForm";
import Image from "next/image";
import Link from "next/link";

export default function PlaceDetailsPage({
  params,
}: {
  params: { id: string };
}) {
  const placeId = params.id;

  const [place, setPlace] = useState<PlaceDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // User reviews state
  const [userReviews, setUserReviews] = useState<UserReviewType[]>([]);
  const [showReviewForm, setShowReviewForm] = useState(false);

  // User complaints state (Shit List)
  const [userComplaints, setUserComplaints] = useState<UserComplaint[]>([]);
  const [showComplaintForm, setShowComplaintForm] = useState(false);

  // Summary bubbles state
  const [topReviews, setTopReviews] = useState<UserReviewType[]>([]);
  const [topComplaints, setTopComplaints] = useState<UserComplaint[]>([]);

  // Split reviews state
  const [splitReviews, setSplitReviews] = useState<SplitReview[]>([]);
  const [showSplitReviewForm, setShowSplitReviewForm] = useState(false);
  const [topSplitGood, setTopSplitGood] = useState<
    Array<{
      review: SplitReview;
      goodStuff: NonNullable<SplitReview["goodStuff"]>;
    }>
  >([]);
  const [topSplitBad, setTopSplitBad] = useState<
    Array<{
      review: SplitReview;
      badStuff: NonNullable<SplitReview["badStuff"]>;
    }>
  >([]);

  // Review form state
  const [newReview, setNewReview] = useState({
    author: "",
    rating: 3,
    text: "",
    mood: "😐",
    vibe: "chill",
    widget: "none",
  });

  // Complaint form state
  const [newComplaint, setNewComplaint] = useState({
    author: "",
    title: "",
    text: "",
    category: "other",
    severity: 3,
  });

  // Function to submit a new user review
  const submitReview = async () => {
    if (!newReview.author.trim() || !newReview.text.trim()) {
      alert("Please fill in your name and review text!");
      return;
    }

    const review: UserReviewType = {
      id: Date.now().toString(),
      author: newReview.author,
      rating: newReview.rating,
      text: newReview.text,
      timestamp: Date.now(),
      mood: newReview.mood,
      vibe: newReview.vibe,
      widget: newReview.widget,
      placeId: placeId,
      votes: 0,
    };

    // Save to localStorage
    saveUserReview(review);

    // Update local state
    setUserReviews((prev) => [review, ...prev]);

    // Reset form
    setNewReview({
      author: "",
      rating: 5,
      text: "",
      mood: "😐",
      vibe: "chill",
      widget: "none",
    });

    setShowReviewForm(false);
  };

  // Function to submit a new complaint
  const submitComplaint = async () => {
    if (
      !newComplaint.author.trim() ||
      !newComplaint.title.trim() ||
      !newComplaint.text.trim()
    ) {
      alert("Please fill in all fields!");
      return;
    }

    const complaint: UserComplaint = {
      id: Date.now().toString(),
      author: newComplaint.author,
      title: newComplaint.title,
      text: newComplaint.text,
      category: newComplaint.category,
      severity: newComplaint.severity,
      timestamp: Date.now(),
      placeId: placeId,
      votes: 0,
    };

    // Save to localStorage
    saveUserComplaint(complaint);

    // Update local state
    setUserComplaints((prev) => [complaint, ...prev]);

    // Reset form
    setNewComplaint({
      author: "",
      title: "",
      text: "",
      category: "other",
      severity: 3,
    });

    setShowComplaintForm(false);
  };

  // Function to handle voting for reviews
  const handleVoteReview = (reviewId: string) => {
    const success = voteForReview(placeId, reviewId);
    if (success) {
      // Refresh reviews and top reviews
      const updatedReviews = getUserReviews(placeId);
      setUserReviews(updatedReviews);
      setTopReviews(getTopReviews(placeId));
    }
  };

  // Function to handle voting for complaints
  const handleVoteComplaint = (complaintId: string) => {
    const success = voteForComplaint(placeId, complaintId);
    if (success) {
      // Refresh complaints and top complaints
      const updatedComplaints = getUserComplaints(placeId);
      setUserComplaints(updatedComplaints);
      setTopComplaints(getTopComplaints(placeId));
    }
  };

  // Function to refresh summary data
  const refreshSummaryData = useCallback(() => {
    setTopReviews(getTopReviews(placeId));
    setTopComplaints(getTopComplaints(placeId));
    setTopSplitGood(getTopSplitGoodStuff(placeId));
    setTopSplitBad(getTopSplitBadStuff(placeId));
  }, [placeId]);

  // Function to handle split review submission
  const handleSplitReviewSubmit = (splitReview: SplitReview) => {
    saveSplitReview(splitReview);

    // Refresh split reviews and summary data
    const updatedSplitReviews = getSplitReviews(placeId);
    setSplitReviews(updatedSplitReviews);
    refreshSummaryData();

    setShowSplitReviewForm(false);
  };

  // Function to handle voting for split reviews
  const handleVoteSplitReview = (reviewId: string, subType: "good" | "bad") => {
    const success = voteForSplitReview(placeId, reviewId, subType);
    if (success) {
      // Refresh split reviews and summary data
      const updatedSplitReviews = getSplitReviews(placeId);
      setSplitReviews(updatedSplitReviews);
      refreshSummaryData();
    }
  };

  useEffect(() => {
    async function fetchPlaceDetails() {
      try {
        const placeDetails = await getPlaceDetails(placeId);
        setPlace(placeDetails);
        setLoading(false);
      } catch (err) {
        console.error("Error fetching place details:", err);
        setError("Failed to load place details. Please try again.");
        setLoading(false);
      }
    }

    fetchPlaceDetails();
  }, [placeId]);

  // Load existing user reviews when component mounts
  useEffect(() => {
    const existingReviews = getUserReviews(placeId);
    setUserReviews(existingReviews);
  }, [placeId]);

  // Load existing user complaints when component mounts
  useEffect(() => {
    const existingComplaints = getUserComplaints(placeId);
    setUserComplaints(existingComplaints);
  }, [placeId]);

  // Load split reviews when component mounts
  useEffect(() => {
    const existingSplitReviews = getSplitReviews(placeId);
    setSplitReviews(existingSplitReviews);
  }, [placeId]);

  // Load summary data when component mounts
  useEffect(() => {
    refreshSummaryData();
  }, [placeId, refreshSummaryData]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
          <p className="mt-2">Loading...</p>
        </div>
      </div>
    );
  }

  if (error || !place) {
    return (
      <div className="min-h-screen p-6 max-w-5xl mx-auto">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error || "Failed to load place details"}
        </div>
        <Link href="/" className="text-blue-600 hover:underline">
          ← Back to search
        </Link>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6 max-w-5xl mx-auto">
      <Link
        href="/"
        className="text-blue-600 hover:underline mb-4 inline-block"
      >
        ← Back to search
      </Link>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden mb-8">
        {place.photos && place.photos[0] && (
          <div className="h-64 relative">
            <Image
              src={`/api/places/photo?photoReference=${place.photos[0].photo_reference}&maxWidth=800`}
              alt={place.name}
              fill
              className="object-cover"
            />
          </div>
        )}

        <div className="p-6">
          <h1 className="text-3xl font-bold mb-2">{place.name}</h1>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            {place.formatted_address}
          </p>

          <div className="flex flex-wrap gap-4 mb-6">
            {place.rating && (
              <div className="flex items-center">
                <div className="flex items-center mr-1">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <span key={i} className="text-yellow-400 text-xl">
                      {i < Math.floor(place.rating || 0) ? "★" : "☆"}
                    </span>
                  ))}
                </div>
                <span className="text-gray-600 dark:text-gray-300">
                  {place.rating} ({place.user_ratings_total || 0} reviews)
                </span>
              </div>
            )}

            {place.website && (
              <a
                href={place.website}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                Visit website
              </a>
            )}

            {place.formatted_phone_number && (
              <span className="text-gray-600 dark:text-gray-300">
                {place.formatted_phone_number}
              </span>
            )}
          </div>

          {place.opening_hours && (
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-2">Opening Hours</h2>
              <ul className="text-gray-600 dark:text-gray-300">
                {place.opening_hours.weekday_text.map((day, index) => (
                  <li key={index}>{day}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>

      {/* Battle Arena: Good vs Bad */}
      {(topReviews.length > 0 ||
        topComplaints.length > 0 ||
        topSplitGood.length > 0 ||
        topSplitBad.length > 0) && (
        <div className="mb-8">
          <div className="text-center mb-6">
            <h2 className="text-4xl font-bold bg-gradient-to-r from-green-600 via-yellow-500 to-red-600 bg-clip-text text-transparent mb-2">
              ⚔️ BATTLE ARENA ⚔️
            </h2>
            <p className="text-gray-600 dark:text-gray-300">
              The community has spoken! Here&apos;s what people love vs what
              they hate most
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Good Side */}
            <div className="bg-gradient-to-br from-green-50 to-emerald-100 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl border-2 border-green-300 dark:border-green-700 p-6">
              <div className="text-center mb-4">
                <h3 className="text-2xl font-bold text-green-800 dark:text-green-200 mb-1">
                  💚 THE GOOD STUFF
                </h3>
                <p className="text-green-600 dark:text-green-300 text-sm">
                  Most helpful positive reviews
                </p>
              </div>

              {topReviews.length > 0 || topSplitGood.length > 0 ? (
                <div className="space-y-4">
                  {/* Traditional Reviews */}
                  {topReviews.slice(0, 2).map((review, index) => (
                    <div
                      key={review.id}
                      className="bg-white/80 dark:bg-gray-800/80 rounded-lg p-4 border border-green-200 dark:border-green-700 relative"
                    >
                      {/* Ranking Badge */}
                      <div className="absolute -top-2 -left-2 bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold text-sm">
                        #{index + 1}
                      </div>

                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="font-semibold text-green-800 dark:text-green-200">
                            {review.author}
                          </span>
                          <div className="flex">
                            {Array.from({ length: review.rating }).map(
                              (_, i) => (
                                <span key={i} className="text-yellow-500">
                                  ⭐
                                </span>
                              )
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 bg-green-100 dark:bg-green-800 px-2 py-1 rounded-full">
                          <span className="text-green-700 dark:text-green-200 text-sm font-medium">
                            👍 {review.votes}
                          </span>
                        </div>
                      </div>

                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                        &ldquo;{review.text}&rdquo;
                      </p>

                      <div className="mt-3 flex items-center justify-between">
                        <span className="text-xs text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-800 px-2 py-1 rounded-full">
                          {review.vibe}
                        </span>
                        <button
                          onClick={() => handleVoteReview(review.id)}
                          disabled={hasUserVoted(placeId, review.id, "review")}
                          className={`text-xs px-3 py-1 rounded-full font-medium transition-all ${
                            hasUserVoted(placeId, review.id, "review")
                              ? "bg-green-200 text-green-800 cursor-not-allowed"
                              : "bg-green-100 text-green-700 hover:bg-green-200 hover:scale-105"
                          }`}
                        >
                          {hasUserVoted(placeId, review.id, "review")
                            ? "✓ Voted"
                            : "👍 Helpful"}
                        </button>
                      </div>
                    </div>
                  ))}

                  {/* Split Review Good Stuff */}
                  {topSplitGood
                    .slice(0, 2)
                    .map(({ review, goodStuff }, index) => (
                      <div
                        key={`split-good-${review.id}`}
                        className="bg-white/80 dark:bg-gray-800/80 rounded-lg p-4 border border-green-200 dark:border-green-700 relative"
                      >
                        {/* Ranking Badge */}
                        <div className="absolute -top-2 -left-2 bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold text-sm">
                          #{topReviews.length + index + 1}
                        </div>

                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <span className="font-semibold text-green-800 dark:text-green-200">
                              {review.author}
                            </span>
                            <div className="flex">
                              {Array.from({ length: review.overallRating }).map(
                                (_, i) => (
                                  <span key={i} className="text-yellow-500">
                                    ⭐
                                  </span>
                                )
                              )}
                            </div>
                            <span className="text-xs bg-purple-100 dark:bg-purple-800 text-purple-700 dark:text-purple-200 px-2 py-1 rounded-full">
                              Split Review
                            </span>
                          </div>
                          <div className="flex items-center space-x-2 bg-green-100 dark:bg-green-800 px-2 py-1 rounded-full">
                            <span className="text-green-700 dark:text-green-200 text-sm font-medium">
                              👍 {goodStuff.votes}
                            </span>
                          </div>
                        </div>

                        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                          &ldquo;{goodStuff.text}&rdquo;
                        </p>

                        <div className="mt-3 flex items-center justify-between">
                          <span className="text-xs text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-800 px-2 py-1 rounded-full">
                            {goodStuff.vibe}
                          </span>
                          <button
                            onClick={() =>
                              handleVoteSplitReview(review.id, "good")
                            }
                            disabled={hasUserVoted(
                              placeId,
                              review.id,
                              "splitReview",
                              "good"
                            )}
                            className={`text-xs px-3 py-1 rounded-full font-medium transition-all ${
                              hasUserVoted(
                                placeId,
                                review.id,
                                "splitReview",
                                "good"
                              )
                                ? "bg-green-200 text-green-800 cursor-not-allowed"
                                : "bg-green-100 text-green-700 hover:bg-green-200 hover:scale-105"
                            }`}
                          >
                            {hasUserVoted(
                              placeId,
                              review.id,
                              "splitReview",
                              "good"
                            )
                              ? "✓ Voted"
                              : "👍 Helpful"}
                          </button>
                        </div>
                      </div>
                    ))}
                </div>
              ) : (
                <div className="text-center py-8 text-green-600 dark:text-green-400">
                  <div className="text-4xl mb-2">🌟</div>
                  <p>No top reviews yet!</p>
                  <p className="text-sm">
                    Be the first to leave a helpful review
                  </p>
                </div>
              )}
            </div>

            {/* Bad Side */}
            <div className="bg-gradient-to-br from-red-50 to-orange-100 dark:from-red-900/20 dark:to-orange-900/20 rounded-xl border-2 border-red-300 dark:border-red-700 p-6">
              <div className="text-center mb-4">
                <h3 className="text-2xl font-bold text-red-800 dark:text-red-200 mb-1">
                  💩 THE SHIT LIST
                </h3>
                <p className="text-red-600 dark:text-red-300 text-sm">
                  Most relatable complaints
                </p>
              </div>

              {topComplaints.length > 0 || topSplitBad.length > 0 ? (
                <div className="space-y-4">
                  {/* Traditional Complaints */}
                  {topComplaints.slice(0, 2).map((complaint, index) => (
                    <div
                      key={complaint.id}
                      className="bg-white/80 dark:bg-gray-800/80 rounded-lg p-4 border border-red-200 dark:border-red-700 relative"
                    >
                      {/* Ranking Badge */}
                      <div className="absolute -top-2 -left-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold text-sm">
                        #{index + 1}
                      </div>

                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="font-semibold text-red-800 dark:text-red-200">
                            {complaint.title}
                          </span>
                          <div className="flex">
                            {Array.from({ length: complaint.severity }).map(
                              (_, i) => (
                                <span key={i} className="text-red-500">
                                  💀
                                </span>
                              )
                            )}
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 bg-red-100 dark:bg-red-800 px-2 py-1 rounded-full">
                          <span className="text-red-700 dark:text-red-200 text-sm font-medium">
                            👎 {complaint.votes}
                          </span>
                        </div>
                      </div>

                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                        &ldquo;{complaint.text}&rdquo;
                      </p>

                      <div className="mt-3 flex items-center justify-between">
                        <span className="text-xs text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-800 px-2 py-1 rounded-full">
                          by {complaint.author}
                        </span>
                        <button
                          onClick={() => handleVoteComplaint(complaint.id)}
                          disabled={hasUserVoted(
                            placeId,
                            complaint.id,
                            "complaint"
                          )}
                          className={`text-xs px-3 py-1 rounded-full font-medium transition-all ${
                            hasUserVoted(placeId, complaint.id, "complaint")
                              ? "bg-red-200 text-red-800 cursor-not-allowed"
                              : "bg-red-100 text-red-700 hover:bg-red-200 hover:scale-105"
                          }`}
                        >
                          {hasUserVoted(placeId, complaint.id, "complaint")
                            ? "✓ Voted"
                            : "👎 Same!"}
                        </button>
                      </div>
                    </div>
                  ))}

                  {/* Split Review Bad Stuff */}
                  {topSplitBad
                    .slice(0, 2)
                    .map(({ review, badStuff }, index) => (
                      <div
                        key={`split-bad-${review.id}`}
                        className="bg-white/80 dark:bg-gray-800/80 rounded-lg p-4 border border-red-200 dark:border-red-700 relative"
                      >
                        {/* Ranking Badge */}
                        <div className="absolute -top-2 -left-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold text-sm">
                          #{topComplaints.length + index + 1}
                        </div>

                        <div className="flex items-start justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <span className="font-semibold text-red-800 dark:text-red-200">
                              {badStuff.title || `${review.author}'s Complaint`}
                            </span>
                            <div className="flex">
                              {Array.from({
                                length: badStuff.severity || 3,
                              }).map((_, i) => (
                                <span key={i} className="text-red-500">
                                  💀
                                </span>
                              ))}
                            </div>
                            <span className="text-xs bg-purple-100 dark:bg-purple-800 text-purple-700 dark:text-purple-200 px-2 py-1 rounded-full">
                              Split Review
                            </span>
                          </div>
                          <div className="flex items-center space-x-2 bg-red-100 dark:bg-red-800 px-2 py-1 rounded-full">
                            <span className="text-red-700 dark:text-red-200 text-sm font-medium">
                              👎 {badStuff.votes}
                            </span>
                          </div>
                        </div>

                        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                          &ldquo;{badStuff.text}&rdquo;
                        </p>

                        <div className="mt-3 flex items-center justify-between">
                          <span className="text-xs text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-800 px-2 py-1 rounded-full">
                            by {review.author}
                          </span>
                          <button
                            onClick={() =>
                              handleVoteSplitReview(review.id, "bad")
                            }
                            disabled={hasUserVoted(
                              placeId,
                              review.id,
                              "splitReview",
                              "bad"
                            )}
                            className={`text-xs px-3 py-1 rounded-full font-medium transition-all ${
                              hasUserVoted(
                                placeId,
                                review.id,
                                "splitReview",
                                "bad"
                              )
                                ? "bg-red-200 text-red-800 cursor-not-allowed"
                                : "bg-red-100 text-red-700 hover:bg-red-200 hover:scale-105"
                            }`}
                          >
                            {hasUserVoted(
                              placeId,
                              review.id,
                              "splitReview",
                              "bad"
                            )
                              ? "✓ Voted"
                              : "👎 Same!"}
                          </button>
                        </div>
                      </div>
                    ))}
                </div>
              ) : (
                <div className="text-center py-8 text-red-600 dark:text-red-400">
                  <div className="text-4xl mb-2">🤷‍♀️</div>
                  <p>No complaints yet!</p>
                  <p className="text-sm">
                    Either this place is perfect or nobody&apos;s speaking up...
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Battle Stats */}
          <div className="mt-6 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-2 border-purple-200 dark:border-purple-700 rounded-xl p-4">
            <div className="text-center">
              <h4 className="font-bold text-purple-800 dark:text-purple-200 mb-2">
                ⚖️ Battle Score
              </h4>
              <div className="flex items-center justify-center space-x-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {topReviews.reduce((sum, review) => sum + review.votes, 0) +
                      topSplitGood.reduce(
                        (sum, item) => sum + item.goodStuff.votes,
                        0
                      )}
                  </div>
                  <div className="text-sm text-green-600">Good Votes</div>
                </div>
                <div className="text-3xl">VS</div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {topComplaints.reduce(
                      (sum, complaint) => sum + complaint.votes,
                      0
                    ) +
                      topSplitBad.reduce(
                        (sum, item) => sum + item.badStuff.votes,
                        0
                      )}
                  </div>
                  <div className="text-sm text-red-600">Bad Votes</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Split Review Form */}
      <div className="mb-8">
        {!showSplitReviewForm ? (
          <div className="bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-6 border-2 border-purple-200 dark:border-purple-700 text-center">
            <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              ✨ Share Your Experience ✨
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Write a split review! Share both the good and bad in one place, or
              just focus on what matters most to you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => setShowSplitReviewForm(true)}
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-3 px-8 rounded-xl transform hover:scale-105 transition-all duration-200"
              >
                ✨ Write Split Review ✨
              </button>
              <button
                onClick={() => setShowReviewForm(true)}
                className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-bold py-3 px-8 rounded-xl transform hover:scale-105 transition-all duration-200"
              >
                🌟 Quick Review 🌟
              </button>
            </div>
          </div>
        ) : (
          <SplitReviewForm
            placeId={placeId}
            onSubmit={handleSplitReviewSubmit}
            onCancel={() => setShowSplitReviewForm(false)}
          />
        )}
      </div>

      {/* Traditional Review Form */}
      {showReviewForm && (
        <div className="mb-8">
          <div className="bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900/20 dark:to-blue-900/20 rounded-xl p-6 border-2 border-green-200 dark:border-green-700">
            <h2 className="text-2xl font-bold mb-4 bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
              🌟 Quick Review 🌟
            </h2>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Your Name
                </label>
                <input
                  type="text"
                  value={newReview.author}
                  onChange={(e) =>
                    setNewReview((prev) => ({
                      ...prev,
                      author: e.target.value,
                    }))
                  }
                  placeholder="What should we call you?"
                  className="w-full p-3 border-2 border-purple-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Rating</label>
                <div className="flex space-x-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      onClick={() =>
                        setNewReview((prev) => ({ ...prev, rating: star }))
                      }
                      className={`text-3xl ${
                        star <= newReview.rating
                          ? "text-yellow-400"
                          : "text-gray-300"
                      } hover:text-yellow-400 transition-colors`}
                    >
                      ★
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Your Review
                </label>
                <textarea
                  value={newReview.text}
                  onChange={(e) =>
                    setNewReview((prev) => ({ ...prev, text: e.target.value }))
                  }
                  placeholder="Tell us about your experience... be real! 💯"
                  rows={4}
                  className="w-full p-3 border-2 border-purple-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Mood</label>
                  <select
                    value={newReview.mood}
                    onChange={(e) =>
                      setNewReview((prev) => ({
                        ...prev,
                        mood: e.target.value,
                      }))
                    }
                    className="w-full p-3 border-2 border-purple-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    {MOOD_OPTIONS.map((mood) => (
                      <option key={mood.value} value={mood.value}>
                        {mood.value} {mood.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Vibe</label>
                  <select
                    value={newReview.vibe}
                    onChange={(e) =>
                      setNewReview((prev) => ({
                        ...prev,
                        vibe: e.target.value,
                      }))
                    }
                    className="w-full p-3 border-2 border-purple-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    {VIBE_OPTIONS.map((vibe) => (
                      <option key={vibe.value} value={vibe.value}>
                        {vibe.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Widget
                  </label>
                  <select
                    value={newReview.widget}
                    onChange={(e) =>
                      setNewReview((prev) => ({
                        ...prev,
                        widget: e.target.value,
                      }))
                    }
                    className="w-full p-3 border-2 border-purple-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    {WIDGET_OPTIONS.map((widget) => (
                      <option key={widget.value} value={widget.value}>
                        {widget.emoji} {widget.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={submitReview}
                  className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-3 px-6 rounded-xl transform hover:scale-105 transition-all duration-200"
                >
                  🚀 Submit Review
                </button>
                <button
                  onClick={() => setShowReviewForm(false)}
                  className="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-xl transition-all duration-200"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Reviews</h2>

        {userReviews.length > 0 ? (
          <div className="space-y-6">
            {userReviews.map((review) => {
              // Get widget emoji
              const widget = WIDGET_OPTIONS.find(
                (w) => w.value === review.widget
              );
              const widgetEmoji = widget?.emoji || "";

              return (
                <div
                  key={review.timestamp}
                  className={`p-6 rounded-xl border-2 border-purple-300 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 dark:border-purple-700 ${
                    review.widget !== "none" ? "relative overflow-hidden" : ""
                  }`}
                  style={{
                    background:
                      review.widget === "rainbow"
                        ? "linear-gradient(45deg, #ff9a9e 0%, #fecfef 25%, #fecfef 50%, #fecfef 75%, #fecfef 100%)"
                        : undefined,
                  }}
                >
                  {/* Widget decorations */}
                  {review.widget === "glitter" && (
                    <div className="absolute inset-0 pointer-events-none">
                      <div className="absolute top-2 left-4 text-yellow-400 animate-pulse">
                        ✨
                      </div>
                      <div className="absolute top-8 right-6 text-yellow-400 animate-pulse delay-300">
                        ✨
                      </div>
                      <div className="absolute bottom-4 left-8 text-yellow-400 animate-pulse delay-700">
                        ✨
                      </div>
                      <div className="absolute bottom-8 right-4 text-yellow-400 animate-pulse delay-500">
                        ✨
                      </div>
                    </div>
                  )}

                  {review.widget === "hearts" && (
                    <div className="absolute inset-0 pointer-events-none">
                      <div className="absolute top-2 right-4 text-pink-400 animate-bounce">
                        💖
                      </div>
                      <div className="absolute bottom-2 left-4 text-pink-400 animate-bounce delay-300">
                        💖
                      </div>
                    </div>
                  )}

                  <div className="flex justify-between items-start mb-3 relative z-10">
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-bold text-lg">{review.author}</h3>
                        {widgetEmoji && (
                          <span className="text-xl">{widgetEmoji}</span>
                        )}
                        <span className="text-lg">{review.mood}</span>
                      </div>
                      <div className="flex items-center space-x-3">
                        <div className="flex">
                          {Array.from({ length: 5 }).map((_, i) => (
                            <span key={i} className="text-yellow-400 text-xl">
                              {i < review.rating ? "★" : "☆"}
                            </span>
                          ))}
                        </div>
                        <span className="text-sm bg-purple-100 text-purple-800 px-2 py-1 rounded-full font-medium">
                          {review.vibe}
                        </span>
                        <span className="text-sm text-gray-500 dark:text-gray-400">
                          {new Date(review.timestamp).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  <p className="text-gray-700 dark:text-gray-300 mb-4 text-lg leading-relaxed">
                    {review.text}
                  </p>

                  {/* Voting Section */}
                  <div className="flex items-center justify-between pt-3 border-t border-purple-200">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Helpful?
                      </span>
                      <button
                        onClick={() => handleVoteReview(review.id)}
                        disabled={hasUserVoted(placeId, review.id, "review")}
                        className={`flex items-center space-x-1 px-3 py-1 rounded-full text-sm font-medium transition-all ${
                          hasUserVoted(placeId, review.id, "review")
                            ? "bg-green-200 text-green-800 cursor-not-allowed"
                            : "bg-green-100 text-green-700 hover:bg-green-200 hover:scale-105"
                        }`}
                      >
                        <span>👍</span>
                        <span>{review.votes}</span>
                      </button>
                    </div>
                    {hasUserVoted(placeId, review.id, "review") && (
                      <span className="text-xs text-green-600 font-medium">
                        ✓ You voted for this
                      </span>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🌟</div>
            <h3 className="text-2xl font-bold text-gray-700 mb-2">
              No reviews yet!
            </h3>
            <p className="text-gray-500 mb-6">
              Be the first to share your experience at this place.
            </p>
            <button
              onClick={() => setShowReviewForm(true)}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-3 px-8 rounded-xl transform hover:scale-105 transition-all duration-200"
            >
              🚀 Write the First Review!
            </button>
          </div>
        )}

        {/* Shit List Section */}
        <div className="mt-12">
          <div className="bg-gradient-to-r from-red-100 to-orange-100 dark:from-red-900/20 dark:to-orange-900/20 rounded-xl p-6 border-2 border-red-300 dark:border-red-700 mb-6">
            <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-red-600 to-orange-600 bg-clip-text text-transparent">
              💩 THE SHIT LIST 💩
            </h2>
            <p className="text-red-700 dark:text-red-300 mb-4">
              Got a major complaint? Drop it here! This is where we keep it 💯
              real about the worst experiences.
            </p>

            {!showComplaintForm ? (
              <button
                onClick={() => setShowComplaintForm(true)}
                className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 text-white font-bold py-3 px-6 rounded-xl transform hover:scale-105 transition-all duration-200"
              >
                🔥 Add to Shit List 🔥
              </button>
            ) : (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Your Name
                    </label>
                    <input
                      type="text"
                      value={newComplaint.author}
                      onChange={(e) =>
                        setNewComplaint((prev) => ({
                          ...prev,
                          author: e.target.value,
                        }))
                      }
                      placeholder="Who's calling out this BS?"
                      className="w-full p-3 border-2 border-red-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Complaint Category
                    </label>
                    <select
                      value={newComplaint.category}
                      onChange={(e) =>
                        setNewComplaint((prev) => ({
                          ...prev,
                          category: e.target.value,
                        }))
                      }
                      className="w-full p-3 border-2 border-red-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    >
                      {COMPLAINT_CATEGORIES.map((category) => (
                        <option key={category.value} value={category.value}>
                          {category.label}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Complaint Title
                  </label>
                  <input
                    type="text"
                    value={newComplaint.title}
                    onChange={(e) =>
                      setNewComplaint((prev) => ({
                        ...prev,
                        title: e.target.value,
                      }))
                    }
                    placeholder="Sum up your complaint in a few words..."
                    className="w-full p-3 border-2 border-red-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Tell Us What Happened
                  </label>
                  <textarea
                    value={newComplaint.text}
                    onChange={(e) =>
                      setNewComplaint((prev) => ({
                        ...prev,
                        text: e.target.value,
                      }))
                    }
                    placeholder="Spill the tea... what went wrong? Don't hold back! 🔥"
                    rows={4}
                    className="w-full p-3 border-2 border-red-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    How Bad Was It? (1-5 💀)
                  </label>
                  <div className="flex space-x-2">
                    {[1, 2, 3, 4, 5].map((level) => (
                      <button
                        key={level}
                        onClick={() =>
                          setNewComplaint((prev) => ({
                            ...prev,
                            severity: level,
                          }))
                        }
                        className={`text-3xl ${
                          level <= newComplaint.severity
                            ? "text-red-500"
                            : "text-gray-300"
                        } hover:text-red-500 transition-colors`}
                      >
                        💀
                      </button>
                    ))}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    {newComplaint.severity === 1 && "Mildly annoying"}
                    {newComplaint.severity === 2 && "Pretty bad"}
                    {newComplaint.severity === 3 && "Really bad"}
                    {newComplaint.severity === 4 && "Absolutely terrible"}
                    {newComplaint.severity === 5 && "WORST EXPERIENCE EVER"}
                  </p>
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={submitComplaint}
                    className="bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 text-white font-bold py-3 px-6 rounded-xl transform hover:scale-105 transition-all duration-200"
                  >
                    💀 Submit to Shit List
                  </button>
                  <button
                    onClick={() => setShowComplaintForm(false)}
                    className="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-xl transition-all duration-200"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Display Complaints */}
          {userComplaints.length > 0 ? (
            <div className="space-y-4">
              <h3 className="text-xl font-bold text-red-600">
                Current Complaints ({userComplaints.length})
              </h3>
              {userComplaints.map((complaint) => {
                const category = COMPLAINT_CATEGORIES.find(
                  (c) => c.value === complaint.category
                );
                const categoryEmoji = category?.emoji || "💩";

                return (
                  <div
                    key={complaint.timestamp}
                    className="p-6 rounded-xl border-2 border-red-300 bg-gradient-to-br from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 dark:border-red-700"
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="font-bold text-lg text-red-800 dark:text-red-200">
                            {complaint.title}
                          </h4>
                          <span className="text-xl">{categoryEmoji}</span>
                        </div>
                        <div className="flex items-center space-x-3">
                          <span className="text-sm font-medium text-red-700 dark:text-red-300">
                            by {complaint.author}
                          </span>
                          <span className="text-sm bg-red-100 text-red-800 px-2 py-1 rounded-full font-medium">
                            {category?.label.replace(/^[^\s]+ /, "") ||
                              "Other BS"}
                          </span>
                          <div className="flex">
                            {Array.from({ length: 5 }).map((_, i) => (
                              <span key={i} className="text-red-500 text-lg">
                                {i < complaint.severity ? "💀" : "☠️"}
                              </span>
                            ))}
                          </div>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {new Date(complaint.timestamp).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>

                    <p className="text-gray-700 dark:text-gray-300 text-lg leading-relaxed mb-4">
                      {complaint.text}
                    </p>

                    {/* Voting Section */}
                    <div className="flex items-center justify-between pt-3 border-t border-red-200">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-gray-600 dark:text-gray-400">
                          This happened to you too?
                        </span>
                        <button
                          onClick={() => handleVoteComplaint(complaint.id)}
                          disabled={hasUserVoted(
                            placeId,
                            complaint.id,
                            "complaint"
                          )}
                          className={`flex items-center space-x-1 px-3 py-1 rounded-full text-sm font-medium transition-all ${
                            hasUserVoted(placeId, complaint.id, "complaint")
                              ? "bg-red-200 text-red-800 cursor-not-allowed"
                              : "bg-red-100 text-red-700 hover:bg-red-200 hover:scale-105"
                          }`}
                        >
                          <span>👎</span>
                          <span>{complaint.votes}</span>
                        </button>
                      </div>
                      {hasUserVoted(placeId, complaint.id, "complaint") && (
                        <span className="text-xs text-red-600 font-medium">
                          ✓ You voted for this
                        </span>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-6xl mb-4">🤷‍♀️</div>
              <h3 className="text-xl font-bold text-gray-700 mb-2">
                No complaints yet!
              </h3>
              <p className="text-gray-500 mb-4">
                Either this place is perfect or nobody&apos;s brave enough to
                speak up...
              </p>
            </div>
          )}
        </div>

        {/* About ghettoreviews */}
        <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 border-2 border-purple-200 rounded-xl text-sm">
          <p className="font-bold text-purple-800 mb-2 text-lg">
            💯 About ghettoreviews 💯
          </p>
          <p className="text-purple-700 mb-2">
            This is a user-generated review site where{" "}
            <strong>real people</strong> share their honest experiences. No
            corporate BS, no paid reviews - just authentic opinions from the
            community.
          </p>
          <p className="text-purple-700 mt-2 font-medium">
            ✨ Add your own review with fun MySpace-style widgets! ✨
          </p>
        </div>
      </div>
    </div>
  );
}

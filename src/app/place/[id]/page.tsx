"use client";

import { useEffect, useState } from "react";
import { getPlaceDetails, PlaceDetails } from "@/utils/googlePlaces";
import Image from "next/image";
import Link from "next/link";

// User review interface
interface UserReview {
  id: string;
  author: string;
  rating: number;
  text: string;
  timestamp: number;
  widget?: string; // For fun MySpace-style widgets
  mood?: string; // User's mood when writing the review
  vibe?: string; // Overall vibe rating
}

export default function PlaceDetailsPage({
  params,
}: {
  params: { id: string };
}) {
  const placeId = params.id;

  const [place, setPlace] = useState<PlaceDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // User reviews state
  const [userReviews, setUserReviews] = useState<UserReview[]>([]);
  const [showReviewForm, setShowReviewForm] = useState(false);

  // Review form state
  const [newReview, setNewReview] = useState({
    author: "",
    rating: 5,
    text: "",
    mood: "😐",
    vibe: "chill",
    widget: "none",
  });

  // Function to submit a new user review
  const submitReview = () => {
    if (!newReview.author.trim() || !newReview.text.trim()) {
      alert("Please fill in your name and review text!");
      return;
    }

    const review: UserReview = {
      id: Date.now().toString(),
      author: newReview.author,
      rating: newReview.rating,
      text: newReview.text,
      timestamp: Date.now(),
      mood: newReview.mood,
      vibe: newReview.vibe,
      widget: newReview.widget,
    };

    setUserReviews((prev) => [review, ...prev]);

    // Reset form
    setNewReview({
      author: "",
      rating: 5,
      text: "",
      mood: "😐",
      vibe: "chill",
      widget: "none",
    });

    setShowReviewForm(false);
  };

  useEffect(() => {
    async function fetchPlaceDetails() {
      try {
        const placeDetails = await getPlaceDetails(placeId);
        setPlace(placeDetails);
        setLoading(false);
      } catch (err) {
        console.error("Error fetching place details:", err);
        setError("Failed to load place details. Please try again.");
        setLoading(false);
      }
    }

    fetchPlaceDetails();
  }, [placeId]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
          <p className="mt-2">Loading...</p>
        </div>
      </div>
    );
  }

  if (error || !place) {
    return (
      <div className="min-h-screen p-6 max-w-5xl mx-auto">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error || "Failed to load place details"}
        </div>
        <Link href="/" className="text-blue-600 hover:underline">
          ← Back to search
        </Link>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6 max-w-5xl mx-auto">
      <Link
        href="/"
        className="text-blue-600 hover:underline mb-4 inline-block"
      >
        ← Back to search
      </Link>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden mb-8">
        {place.photos && place.photos[0] && (
          <div className="h-64 relative">
            <Image
              src={`/api/places/photo?photoReference=${place.photos[0].photo_reference}&maxWidth=800`}
              alt={place.name}
              fill
              className="object-cover"
            />
          </div>
        )}

        <div className="p-6">
          <h1 className="text-3xl font-bold mb-2">{place.name}</h1>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            {place.formatted_address}
          </p>

          <div className="flex flex-wrap gap-4 mb-6">
            {place.rating && (
              <div className="flex items-center">
                <div className="flex items-center mr-1">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <span key={i} className="text-yellow-400 text-xl">
                      {i < Math.floor(place.rating || 0) ? "★" : "☆"}
                    </span>
                  ))}
                </div>
                <span className="text-gray-600 dark:text-gray-300">
                  {place.rating} ({place.user_ratings_total || 0} reviews)
                </span>
              </div>
            )}

            {place.website && (
              <a
                href={place.website}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                Visit website
              </a>
            )}

            {place.formatted_phone_number && (
              <span className="text-gray-600 dark:text-gray-300">
                {place.formatted_phone_number}
              </span>
            )}
          </div>

          {place.opening_hours && (
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-2">Opening Hours</h2>
              <ul className="text-gray-600 dark:text-gray-300">
                {place.opening_hours.weekday_text.map((day, index) => (
                  <li key={index}>{day}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Reviews</h2>

        {allReviews.length > 0 ? (
          <>
            {/* Review Analysis Summary */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
              <h3 className="text-lg font-semibold mb-3">
                Review Analysis Summary
              </h3>

              {/* Calculate statistics */}
              {(() => {
                const totalReviews = totalReviewCount || allReviews.length;
                const fakeReviews = Array.from(analyzedReviews.values()).filter(
                  (r) => r.isFake
                ).length;
                const realReviews = totalReviews - fakeReviews;
                const fakePercentage = (fakeReviews / totalReviews) * 100;
                const realPercentage = (realReviews / totalReviews) * 100;

                // Count detection methods
                const repetitivePhrasing = Array.from(
                  analyzedReviews.values()
                ).filter(
                  (r) => r.repetitivePhrasingResult?.isSuspicious
                ).length;
                const keywordOveruse = Array.from(
                  analyzedReviews.values()
                ).filter((r) => r.keywordOveruseResult?.isSuspicious).length;
                const sentimentExtremes = Array.from(
                  analyzedReviews.values()
                ).filter((r) => r.sentimentExtremesResult?.isSuspicious).length;

                return (
                  <div>
                    <div className="flex mb-4">
                      <div className="w-1/2 pr-2">
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">
                            Real Reviews
                          </span>
                          <span className="text-sm font-medium">
                            {realPercentage.toFixed(0)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-green-600 h-2.5 rounded-full"
                            style={{ width: `${realPercentage}%` }}
                          ></div>
                        </div>
                        <p className="text-sm mt-1">
                          {realReviews} of {totalReviews} reviews
                        </p>
                      </div>

                      <div className="w-1/2 pl-2">
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">
                            Fake Reviews
                          </span>
                          <span className="text-sm font-medium">
                            {fakePercentage.toFixed(0)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-red-600 h-2.5 rounded-full"
                            style={{ width: `${fakePercentage}%` }}
                          ></div>
                        </div>
                        <p className="text-sm mt-1">
                          {fakeReviews} of {totalReviews} reviews
                        </p>
                      </div>
                    </div>

                    <div className="mt-4">
                      <h4 className="text-sm font-medium mb-2">
                        Detection Methods
                      </h4>
                      <div className="grid grid-cols-3 gap-2 text-xs">
                        <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded">
                          <p className="font-medium">Repetitive Phrasing</p>
                          <p>{repetitivePhrasing} reviews flagged</p>
                        </div>
                        <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded">
                          <p className="font-medium">Keyword Overuse</p>
                          <p>{keywordOveruse} reviews flagged</p>
                        </div>
                        <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded">
                          <p className="font-medium">Sentiment Extremes</p>
                          <p>{sentimentExtremes} reviews flagged</p>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 text-sm">
                      <p className="italic text-gray-600 dark:text-gray-300">
                        Note: Reviews may be flagged by multiple detection
                        methods.
                      </p>
                    </div>
                  </div>
                );
              })()}
            </div>

            <div className="space-y-6">
              {allReviews.map((review) => {
                const analysis = analyzedReviews.get(review.time);
                const isFake = analysis?.isFake || false;
                const confidence = analysis?.confidence || 0;
                const reasons = analysis?.reasons || [];

                return (
                  <div
                    key={review.time}
                    className={`p-4 rounded-lg border ${
                      isFake
                        ? "border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800"
                        : "border-green-200 bg-green-50 dark:bg-green-900/20 dark:border-green-800"
                    }`}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="font-semibold">{review.author_name}</h3>
                        <div className="flex items-center">
                          <div className="flex mr-2">
                            {Array.from({ length: 5 }).map((_, i) => (
                              <span key={i} className="text-yellow-400">
                                {i < review.rating ? "★" : "☆"}
                              </span>
                            ))}
                          </div>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {review.relative_time_description}
                          </span>
                        </div>
                      </div>

                      <span
                        className={`px-2 py-1 text-xs font-medium rounded ${
                          isFake
                            ? "bg-red-200 text-red-800 dark:bg-red-800 dark:text-red-200"
                            : "bg-green-200 text-green-800 dark:bg-green-800 dark:text-green-200"
                        }`}
                      >
                        {isFake ? "Likely Fake" : "Likely Real"}
                      </span>
                    </div>

                    <p className="text-gray-700 dark:text-gray-300 mb-2">
                      {review.text}
                    </p>

                    {analysis && (
                      <div className="mt-4 text-sm border-t pt-3">
                        <div className="flex justify-between items-center mb-2">
                          <p className="font-medium">
                            Overall confidence: {(confidence * 100).toFixed(0)}%
                          </p>
                          <button
                            className="text-blue-600 text-xs hover:underline"
                            onClick={() => {
                              const detailsEl = document.getElementById(
                                `review-details-${review.time}`
                              );
                              if (detailsEl) {
                                detailsEl.classList.toggle("hidden");
                              }
                            }}
                          >
                            Show/Hide Details
                          </button>
                        </div>

                        <div
                          id={`review-details-${review.time}`}
                          className="hidden"
                        >
                          {/* Basic Analysis */}
                          <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                            <p className="font-medium mb-1">Basic Analysis:</p>
                            <div className="flex items-center mb-1">
                              <span
                                className={`w-3 h-3 rounded-full mr-2 ${
                                  analysis.basicAnalysis.isFake
                                    ? "bg-red-500"
                                    : "bg-green-500"
                                }`}
                              ></span>
                              <span>
                                {analysis.basicAnalysis.isFake
                                  ? "Likely Fake"
                                  : "Likely Real"}
                                (
                                {(
                                  analysis.basicAnalysis.confidence * 100
                                ).toFixed(0)}
                                % confidence)
                              </span>
                            </div>
                            {analysis.basicAnalysis.reasons.length > 0 && (
                              <ul className="list-disc list-inside text-xs ml-5">
                                {analysis.basicAnalysis.reasons.map(
                                  (reason, index) => (
                                    <li key={index}>{reason}</li>
                                  )
                                )}
                              </ul>
                            )}
                          </div>

                          {/* Repetitive Phrasing */}
                          {analysis.repetitivePhrasingResult && (
                            <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                              <p className="font-medium mb-1">
                                Repetitive Phrasing Detection:
                              </p>
                              <div className="flex items-center mb-1">
                                <span
                                  className={`w-3 h-3 rounded-full mr-2 ${
                                    analysis.repetitivePhrasingResult
                                      .isSuspicious
                                      ? "bg-red-500"
                                      : "bg-green-500"
                                  }`}
                                ></span>
                                <span>
                                  {analysis.repetitivePhrasingResult
                                    .isSuspicious
                                    ? "Suspicious: Similar to other reviews"
                                    : "Unique content"}
                                </span>
                              </div>
                              {analysis.repetitivePhrasingResult.similarReviews
                                .length > 0 && (
                                <p className="text-xs ml-5">
                                  Similar to{" "}
                                  {
                                    analysis.repetitivePhrasingResult
                                      .similarReviews.length
                                  }{" "}
                                  other review(s) (Similarity score:{" "}
                                  {(
                                    analysis.repetitivePhrasingResult
                                      .similarityScore * 100
                                  ).toFixed(0)}
                                  %)
                                </p>
                              )}
                            </div>
                          )}

                          {/* Keyword Overuse */}
                          {analysis.keywordOveruseResult && (
                            <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                              <p className="font-medium mb-1">
                                Keyword Overuse Detection:
                              </p>
                              <div className="flex items-center mb-1">
                                <span
                                  className={`w-3 h-3 rounded-full mr-2 ${
                                    analysis.keywordOveruseResult.isSuspicious
                                      ? "bg-red-500"
                                      : "bg-green-500"
                                  }`}
                                ></span>
                                <span>
                                  {analysis.keywordOveruseResult.isSuspicious
                                    ? "Suspicious: Overuses generic words"
                                    : "Natural language usage"}
                                </span>
                              </div>
                              {analysis.keywordOveruseResult.suspectWords
                                .length > 0 && (
                                <div className="text-xs ml-5">
                                  <p>
                                    Overuse score:{" "}
                                    {(
                                      analysis.keywordOveruseResult
                                        .overuseScore * 100
                                    ).toFixed(0)}
                                    %
                                  </p>
                                  <p>
                                    Suspect words:{" "}
                                    {analysis.keywordOveruseResult.suspectWords
                                      .map((w) => `${w.word} (${w.count})`)
                                      .join(", ")}
                                  </p>
                                </div>
                              )}
                            </div>
                          )}

                          {/* Sentiment Extremes */}
                          {analysis.sentimentExtremesResult && (
                            <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                              <p className="font-medium mb-1">
                                Sentiment Extremes Detection:
                              </p>
                              <div className="flex items-center mb-1">
                                <span
                                  className={`w-3 h-3 rounded-full mr-2 ${
                                    analysis.sentimentExtremesResult
                                      .isSuspicious
                                      ? "bg-red-500"
                                      : "bg-green-500"
                                  }`}
                                ></span>
                                <span>
                                  {analysis.sentimentExtremesResult.isSuspicious
                                    ? "Suspicious: Extreme sentiment"
                                    : "Balanced sentiment"}
                                </span>
                              </div>
                              <div className="text-xs ml-5">
                                <p>
                                  Sentiment:{" "}
                                  {analysis.sentimentExtremesResult
                                    .sentimentScore > 0
                                    ? `Positive (${(
                                        analysis.sentimentExtremesResult
                                          .sentimentScore * 100
                                      ).toFixed(0)}%)`
                                    : `Negative (${(
                                        Math.abs(
                                          analysis.sentimentExtremesResult
                                            .sentimentScore
                                        ) * 100
                                      ).toFixed(0)}%)`}
                                </p>
                                <p>
                                  Extreme score:{" "}
                                  {(
                                    analysis.sentimentExtremesResult
                                      .extremeScore * 100
                                  ).toFixed(0)}
                                  %
                                </p>
                                {analysis.sentimentExtremesResult.reasons
                                  .length > 0 && (
                                  <ul className="list-disc list-inside">
                                    {analysis.sentimentExtremesResult.reasons.map(
                                      (reason, index) => (
                                        <li key={index}>{reason}</li>
                                      )
                                    )}
                                  </ul>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Cache Comparison */}
                          {analysis.cacheComparisonResult && (
                            <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                              <p className="font-medium mb-1">
                                Cache Comparison:
                              </p>
                              <div className="flex items-center mb-1">
                                <span
                                  className={`w-3 h-3 rounded-full mr-2 ${
                                    analysis.cacheComparisonResult
                                      .fakeSimScore >
                                    analysis.cacheComparisonResult.realSimScore
                                      ? "bg-red-500"
                                      : "bg-green-500"
                                  }`}
                                ></span>
                                <span>
                                  {analysis.cacheComparisonResult.fakeSimScore >
                                  analysis.cacheComparisonResult.realSimScore
                                    ? "Suspicious: Similar to known fake reviews"
                                    : "Similar to known real reviews"}
                                </span>
                              </div>
                              <div className="text-xs ml-5">
                                <p>
                                  Similarity to fake reviews:{" "}
                                  {(
                                    analysis.cacheComparisonResult
                                      .fakeSimScore * 100
                                  ).toFixed(0)}
                                  %
                                </p>
                                <p>
                                  Similarity to real reviews:{" "}
                                  {(
                                    analysis.cacheComparisonResult
                                      .realSimScore * 100
                                  ).toFixed(0)}
                                  %
                                </p>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Summary of reasons */}
                        {reasons.length > 0 && (
                          <div className="mt-1">
                            <p className="font-medium">Key findings:</p>
                            <ul className="list-disc list-inside">
                              {reasons.slice(0, 3).map((reason, index) => (
                                <li key={index}>{reason}</li>
                              ))}
                              {reasons.length > 3 && (
                                <li className="text-gray-500">
                                  ...and {reasons.length - 3} more (see details)
                                </li>
                              )}
                            </ul>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {/* Loading Status and Load More Button */}
            <div className="mt-8 text-center">
              {loadingMoreReviews ? (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg inline-block">
                  <div className="flex items-center">
                    <span className="inline-block h-5 w-5 animate-spin rounded-full border-2 border-solid border-blue-600 border-r-transparent align-[-0.125em] mr-3"></span>
                    <span className="text-blue-800">
                      Loading and analyzing reviews... {allReviews.length} of{" "}
                      {totalReviewCount} loaded
                    </span>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg inline-block mb-4">
                    <span className="text-green-800">
                      {allReviews.length === totalReviewCount
                        ? `All ${totalReviewCount} reviews loaded and analyzed`
                        : `${allReviews.length} of ${totalReviewCount} reviews loaded and analyzed`}
                    </span>
                  </div>

                  {hasMoreReviews && (
                    <button
                      onClick={loadAllReviews}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg"
                    >
                      Load More Reviews
                    </button>
                  )}
                </div>
              )}
            </div>

            {/* API Information Notice */}
            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg text-sm">
              <p className="font-medium text-blue-800 mb-1">
                About Review Analysis:
              </p>
              <p className="text-blue-700">
                We're using the Featurable API to fetch all available reviews
                for this business. This allows us to analyze a comprehensive set
                of reviews beyond the 5-review limit of the standard Google
                Places API.
              </p>
              <p className="text-blue-700 mt-2">
                <strong>Analysis is performed on all available reviews</strong>{" "}
                using multiple detection techniques: repetitive phrasing,
                keyword overuse, and sentiment extremes analysis. Duplicate
                detection has been implemented to ensure unique reviews.
              </p>
            </div>
          </>
        ) : (
          <p className="text-gray-500">No reviews available for this place.</p>
        )}
      </div>
    </div>
  );
}

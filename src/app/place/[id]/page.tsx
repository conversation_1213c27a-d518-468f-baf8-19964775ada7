"use client";

import { useEffect, useState } from "react";
import {
  getPlaceDetails,
  PlaceDetails,
  PlaceReview,
} from "@/utils/googlePlaces";
import { analyzeReview, ReviewAnalysis } from "@/utils/reviewAnalysis";
import {
  saveUserReview,
  getUserReviews,
  UserReview as UserReviewType,
  WIDGET_OPTIONS,
  MOOD_OPTIONS,
  VIBE_OPTIONS,
} from "@/utils/userReviews";
import Image from "next/image";
import Link from "next/link";

export default function PlaceDetailsPage({
  params,
}: {
  params: { id: string };
}) {
  const placeId = params.id;

  const [place, setPlace] = useState<PlaceDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // User reviews state
  const [userReviews, setUserReviews] = useState<UserReviewType[]>([]);
  const [showReviewForm, setShowReviewForm] = useState(false);

  // Review analysis state
  const [allReviews, setAllReviews] = useState<UserReviewType[]>([]);
  const [analyzedReviews, setAnalyzedReviews] = useState<
    Map<number, ReviewAnalysis>
  >(new Map());
  const [totalReviewCount, setTotalReviewCount] = useState(0);
  const [loadingMoreReviews, setLoadingMoreReviews] = useState(false);
  const [hasMoreReviews, setHasMoreReviews] = useState(false);

  // Review form state
  const [newReview, setNewReview] = useState({
    author: "",
    rating: 5,
    text: "",
    mood: "😐",
    vibe: "chill",
    widget: "none",
  });

  // Function to submit a new user review
  const submitReview = async () => {
    if (!newReview.author.trim() || !newReview.text.trim()) {
      alert("Please fill in your name and review text!");
      return;
    }

    const review: UserReviewType = {
      id: Date.now().toString(),
      author: newReview.author,
      rating: newReview.rating,
      text: newReview.text,
      timestamp: Date.now(),
      mood: newReview.mood,
      vibe: newReview.vibe,
      widget: newReview.widget,
      placeId: placeId,
    };

    // Save to localStorage
    saveUserReview(review);

    // Update local state
    setUserReviews((prev) => [review, ...prev]);
    setAllReviews((prev) => [review, ...prev]);
    setTotalReviewCount((prev) => prev + 1);

    // Analyze the new review
    try {
      const analysis = await analyzeReview(
        {
          author_name: review.author,
          rating: review.rating,
          text: review.text,
          time: review.timestamp,
          relative_time_description: "just now",
        },
        allReviews.map((r) => ({
          author_name: r.author,
          rating: r.rating,
          text: r.text,
          time: r.timestamp,
          relative_time_description: "user review",
        })),
        placeId
      );

      setAnalyzedReviews((prev) =>
        new Map(prev).set(review.timestamp, analysis)
      );
    } catch (error) {
      console.error("Error analyzing review:", error);
    }

    // Reset form
    setNewReview({
      author: "",
      rating: 5,
      text: "",
      mood: "😐",
      vibe: "chill",
      widget: "none",
    });

    setShowReviewForm(false);
  };

  // Load all reviews function (placeholder for now)
  const loadAllReviews = () => {
    // This would load more reviews if we had external sources
    // For now, just hide the button
    setHasMoreReviews(false);
  };

  useEffect(() => {
    async function fetchPlaceDetails() {
      try {
        const placeDetails = await getPlaceDetails(placeId);
        setPlace(placeDetails);
        setLoading(false);
      } catch (err) {
        console.error("Error fetching place details:", err);
        setError("Failed to load place details. Please try again.");
        setLoading(false);
      }
    }

    fetchPlaceDetails();
  }, [placeId]);

  // Load existing user reviews when component mounts
  useEffect(() => {
    const existingReviews = getUserReviews(placeId);
    setUserReviews(existingReviews);
    setAllReviews(existingReviews);
    setTotalReviewCount(existingReviews.length);

    // Analyze existing reviews
    if (existingReviews.length > 0) {
      const analyzeExistingReviews = async () => {
        const analysisMap = new Map<number, ReviewAnalysis>();

        for (const review of existingReviews) {
          try {
            const analysis = await analyzeReview(
              {
                author_name: review.author,
                rating: review.rating,
                text: review.text,
                time: review.timestamp,
                relative_time_description: "user review",
              },
              existingReviews.map((r) => ({
                author_name: r.author,
                rating: r.rating,
                text: r.text,
                time: r.timestamp,
                relative_time_description: "user review",
              })),
              placeId
            );

            analysisMap.set(review.timestamp, analysis);
          } catch (error) {
            console.error("Error analyzing existing review:", error);
          }
        }

        setAnalyzedReviews(analysisMap);
      };

      analyzeExistingReviews();
    }
  }, [placeId]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
          <p className="mt-2">Loading...</p>
        </div>
      </div>
    );
  }

  if (error || !place) {
    return (
      <div className="min-h-screen p-6 max-w-5xl mx-auto">
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error || "Failed to load place details"}
        </div>
        <Link href="/" className="text-blue-600 hover:underline">
          ← Back to search
        </Link>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6 max-w-5xl mx-auto">
      <Link
        href="/"
        className="text-blue-600 hover:underline mb-4 inline-block"
      >
        ← Back to search
      </Link>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden mb-8">
        {place.photos && place.photos[0] && (
          <div className="h-64 relative">
            <Image
              src={`/api/places/photo?photoReference=${place.photos[0].photo_reference}&maxWidth=800`}
              alt={place.name}
              fill
              className="object-cover"
            />
          </div>
        )}

        <div className="p-6">
          <h1 className="text-3xl font-bold mb-2">{place.name}</h1>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            {place.formatted_address}
          </p>

          <div className="flex flex-wrap gap-4 mb-6">
            {place.rating && (
              <div className="flex items-center">
                <div className="flex items-center mr-1">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <span key={i} className="text-yellow-400 text-xl">
                      {i < Math.floor(place.rating || 0) ? "★" : "☆"}
                    </span>
                  ))}
                </div>
                <span className="text-gray-600 dark:text-gray-300">
                  {place.rating} ({place.user_ratings_total || 0} reviews)
                </span>
              </div>
            )}

            {place.website && (
              <a
                href={place.website}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                Visit website
              </a>
            )}

            {place.formatted_phone_number && (
              <span className="text-gray-600 dark:text-gray-300">
                {place.formatted_phone_number}
              </span>
            )}
          </div>

          {place.opening_hours && (
            <div className="mb-6">
              <h2 className="text-xl font-semibold mb-2">Opening Hours</h2>
              <ul className="text-gray-600 dark:text-gray-300">
                {place.opening_hours.weekday_text.map((day, index) => (
                  <li key={index}>{day}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>

      {/* User Review Form */}
      <div className="mb-8">
        <div className="bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl p-6 border-2 border-purple-200 dark:border-purple-700">
          <h2 className="text-2xl font-bold mb-4 bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
            ✨ Drop Your Review ✨
          </h2>

          {!showReviewForm ? (
            <button
              onClick={() => setShowReviewForm(true)}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-3 px-6 rounded-xl transform hover:scale-105 transition-all duration-200"
            >
              🌟 Write a Review 🌟
            </button>
          ) : (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Your Name
                </label>
                <input
                  type="text"
                  value={newReview.author}
                  onChange={(e) =>
                    setNewReview((prev) => ({
                      ...prev,
                      author: e.target.value,
                    }))
                  }
                  placeholder="What should we call you?"
                  className="w-full p-3 border-2 border-purple-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Rating</label>
                <div className="flex space-x-2">
                  {[1, 2, 3, 4, 5].map((star) => (
                    <button
                      key={star}
                      onClick={() =>
                        setNewReview((prev) => ({ ...prev, rating: star }))
                      }
                      className={`text-3xl ${
                        star <= newReview.rating
                          ? "text-yellow-400"
                          : "text-gray-300"
                      } hover:text-yellow-400 transition-colors`}
                    >
                      ★
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  Your Review
                </label>
                <textarea
                  value={newReview.text}
                  onChange={(e) =>
                    setNewReview((prev) => ({ ...prev, text: e.target.value }))
                  }
                  placeholder="Tell us about your experience... be real! 💯"
                  rows={4}
                  className="w-full p-3 border-2 border-purple-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Mood</label>
                  <select
                    value={newReview.mood}
                    onChange={(e) =>
                      setNewReview((prev) => ({
                        ...prev,
                        mood: e.target.value,
                      }))
                    }
                    className="w-full p-3 border-2 border-purple-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    {MOOD_OPTIONS.map((mood) => (
                      <option key={mood.value} value={mood.value}>
                        {mood.value} {mood.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Vibe</label>
                  <select
                    value={newReview.vibe}
                    onChange={(e) =>
                      setNewReview((prev) => ({
                        ...prev,
                        vibe: e.target.value,
                      }))
                    }
                    className="w-full p-3 border-2 border-purple-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    {VIBE_OPTIONS.map((vibe) => (
                      <option key={vibe.value} value={vibe.value}>
                        {vibe.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">
                    Widget
                  </label>
                  <select
                    value={newReview.widget}
                    onChange={(e) =>
                      setNewReview((prev) => ({
                        ...prev,
                        widget: e.target.value,
                      }))
                    }
                    className="w-full p-3 border-2 border-purple-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-pink-500"
                  >
                    {WIDGET_OPTIONS.map((widget) => (
                      <option key={widget.value} value={widget.value}>
                        {widget.emoji} {widget.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  onClick={submitReview}
                  className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600 text-white font-bold py-3 px-6 rounded-xl transform hover:scale-105 transition-all duration-200"
                >
                  🚀 Submit Review
                </button>
                <button
                  onClick={() => setShowReviewForm(false)}
                  className="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-xl transition-all duration-200"
                >
                  Cancel
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="mb-8">
        <h2 className="text-2xl font-bold mb-4">Reviews</h2>

        {allReviews.length > 0 ? (
          <>
            {/* Review Analysis Summary */}
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-6">
              <h3 className="text-lg font-semibold mb-3">
                Review Analysis Summary
              </h3>

              {/* Calculate statistics */}
              {(() => {
                const totalReviews = totalReviewCount || allReviews.length;
                const fakeReviews = Array.from(analyzedReviews.values()).filter(
                  (r) => r.isFake
                ).length;
                const realReviews = totalReviews - fakeReviews;
                const fakePercentage = (fakeReviews / totalReviews) * 100;
                const realPercentage = (realReviews / totalReviews) * 100;

                // Count detection methods
                const repetitivePhrasing = Array.from(
                  analyzedReviews.values()
                ).filter(
                  (r) => r.repetitivePhrasingResult?.isSuspicious
                ).length;
                const keywordOveruse = Array.from(
                  analyzedReviews.values()
                ).filter((r) => r.keywordOveruseResult?.isSuspicious).length;
                const sentimentExtremes = Array.from(
                  analyzedReviews.values()
                ).filter((r) => r.sentimentExtremesResult?.isSuspicious).length;

                return (
                  <div>
                    <div className="flex mb-4">
                      <div className="w-1/2 pr-2">
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">
                            Real Reviews
                          </span>
                          <span className="text-sm font-medium">
                            {realPercentage.toFixed(0)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-green-600 h-2.5 rounded-full"
                            style={{ width: `${realPercentage}%` }}
                          ></div>
                        </div>
                        <p className="text-sm mt-1">
                          {realReviews} of {totalReviews} reviews
                        </p>
                      </div>

                      <div className="w-1/2 pl-2">
                        <div className="flex justify-between mb-1">
                          <span className="text-sm font-medium">
                            Fake Reviews
                          </span>
                          <span className="text-sm font-medium">
                            {fakePercentage.toFixed(0)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2.5">
                          <div
                            className="bg-red-600 h-2.5 rounded-full"
                            style={{ width: `${fakePercentage}%` }}
                          ></div>
                        </div>
                        <p className="text-sm mt-1">
                          {fakeReviews} of {totalReviews} reviews
                        </p>
                      </div>
                    </div>

                    <div className="mt-4">
                      <h4 className="text-sm font-medium mb-2">
                        Detection Methods
                      </h4>
                      <div className="grid grid-cols-3 gap-2 text-xs">
                        <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded">
                          <p className="font-medium">Repetitive Phrasing</p>
                          <p>{repetitivePhrasing} reviews flagged</p>
                        </div>
                        <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded">
                          <p className="font-medium">Keyword Overuse</p>
                          <p>{keywordOveruse} reviews flagged</p>
                        </div>
                        <div className="p-2 bg-gray-50 dark:bg-gray-700 rounded">
                          <p className="font-medium">Sentiment Extremes</p>
                          <p>{sentimentExtremes} reviews flagged</p>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 text-sm">
                      <p className="italic text-gray-600 dark:text-gray-300">
                        Note: Reviews may be flagged by multiple detection
                        methods.
                      </p>
                    </div>
                  </div>
                );
              })()}
            </div>

            <div className="space-y-6">
              {allReviews.map((review) => {
                const analysis = analyzedReviews.get(review.timestamp);
                const isFake = analysis?.isFake || false;
                const confidence = analysis?.confidence || 0;
                const reasons = analysis?.reasons || [];

                // Get widget emoji
                const widget = WIDGET_OPTIONS.find(
                  (w) => w.value === review.widget
                );
                const widgetEmoji = widget?.emoji || "";

                return (
                  <div
                    key={review.timestamp}
                    className={`p-6 rounded-xl border-2 ${
                      isFake
                        ? "border-red-300 bg-red-50 dark:bg-red-900/20 dark:border-red-700"
                        : "border-green-300 bg-green-50 dark:bg-green-900/20 dark:border-green-700"
                    } ${
                      review.widget !== "none" ? "relative overflow-hidden" : ""
                    }`}
                    style={{
                      background:
                        review.widget === "rainbow"
                          ? "linear-gradient(45deg, #ff9a9e 0%, #fecfef 25%, #fecfef 50%, #fecfef 75%, #fecfef 100%)"
                          : undefined,
                    }}
                  >
                    {/* Widget decorations */}
                    {review.widget === "glitter" && (
                      <div className="absolute inset-0 pointer-events-none">
                        <div className="absolute top-2 left-4 text-yellow-400 animate-pulse">
                          ✨
                        </div>
                        <div className="absolute top-8 right-6 text-yellow-400 animate-pulse delay-300">
                          ✨
                        </div>
                        <div className="absolute bottom-4 left-8 text-yellow-400 animate-pulse delay-700">
                          ✨
                        </div>
                        <div className="absolute bottom-8 right-4 text-yellow-400 animate-pulse delay-500">
                          ✨
                        </div>
                      </div>
                    )}

                    {review.widget === "hearts" && (
                      <div className="absolute inset-0 pointer-events-none">
                        <div className="absolute top-2 right-4 text-pink-400 animate-bounce">
                          💖
                        </div>
                        <div className="absolute bottom-2 left-4 text-pink-400 animate-bounce delay-300">
                          💖
                        </div>
                      </div>
                    )}

                    <div className="flex justify-between items-start mb-3 relative z-10">
                      <div>
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="font-bold text-lg">{review.author}</h3>
                          {widgetEmoji && (
                            <span className="text-xl">{widgetEmoji}</span>
                          )}
                          <span className="text-lg">{review.mood}</span>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="flex">
                            {Array.from({ length: 5 }).map((_, i) => (
                              <span key={i} className="text-yellow-400 text-xl">
                                {i < review.rating ? "★" : "☆"}
                              </span>
                            ))}
                          </div>
                          <span className="text-sm bg-purple-100 text-purple-800 px-2 py-1 rounded-full font-medium">
                            {review.vibe}
                          </span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">
                            {new Date(review.timestamp).toLocaleDateString()}
                          </span>
                        </div>
                      </div>

                      <span
                        className={`px-2 py-1 text-xs font-medium rounded ${
                          isFake
                            ? "bg-red-200 text-red-800 dark:bg-red-800 dark:text-red-200"
                            : "bg-green-200 text-green-800 dark:bg-green-800 dark:text-green-200"
                        }`}
                      >
                        {isFake ? "Likely Fake" : "Likely Real"}
                      </span>
                    </div>

                    <p className="text-gray-700 dark:text-gray-300 mb-2">
                      {review.text}
                    </p>

                    {analysis && (
                      <div className="mt-4 text-sm border-t pt-3">
                        <div className="flex justify-between items-center mb-2">
                          <p className="font-medium">
                            Overall confidence: {(confidence * 100).toFixed(0)}%
                          </p>
                          <button
                            className="text-blue-600 text-xs hover:underline"
                            onClick={() => {
                              const detailsEl = document.getElementById(
                                `review-details-${review.time}`
                              );
                              if (detailsEl) {
                                detailsEl.classList.toggle("hidden");
                              }
                            }}
                          >
                            Show/Hide Details
                          </button>
                        </div>

                        <div
                          id={`review-details-${review.time}`}
                          className="hidden"
                        >
                          {/* Basic Analysis */}
                          <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                            <p className="font-medium mb-1">Basic Analysis:</p>
                            <div className="flex items-center mb-1">
                              <span
                                className={`w-3 h-3 rounded-full mr-2 ${
                                  analysis.basicAnalysis.isFake
                                    ? "bg-red-500"
                                    : "bg-green-500"
                                }`}
                              ></span>
                              <span>
                                {analysis.basicAnalysis.isFake
                                  ? "Likely Fake"
                                  : "Likely Real"}
                                (
                                {(
                                  analysis.basicAnalysis.confidence * 100
                                ).toFixed(0)}
                                % confidence)
                              </span>
                            </div>
                            {analysis.basicAnalysis.reasons.length > 0 && (
                              <ul className="list-disc list-inside text-xs ml-5">
                                {analysis.basicAnalysis.reasons.map(
                                  (reason, index) => (
                                    <li key={index}>{reason}</li>
                                  )
                                )}
                              </ul>
                            )}
                          </div>

                          {/* Repetitive Phrasing */}
                          {analysis.repetitivePhrasingResult && (
                            <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                              <p className="font-medium mb-1">
                                Repetitive Phrasing Detection:
                              </p>
                              <div className="flex items-center mb-1">
                                <span
                                  className={`w-3 h-3 rounded-full mr-2 ${
                                    analysis.repetitivePhrasingResult
                                      .isSuspicious
                                      ? "bg-red-500"
                                      : "bg-green-500"
                                  }`}
                                ></span>
                                <span>
                                  {analysis.repetitivePhrasingResult
                                    .isSuspicious
                                    ? "Suspicious: Similar to other reviews"
                                    : "Unique content"}
                                </span>
                              </div>
                              {analysis.repetitivePhrasingResult.similarReviews
                                .length > 0 && (
                                <p className="text-xs ml-5">
                                  Similar to{" "}
                                  {
                                    analysis.repetitivePhrasingResult
                                      .similarReviews.length
                                  }{" "}
                                  other review(s) (Similarity score:{" "}
                                  {(
                                    analysis.repetitivePhrasingResult
                                      .similarityScore * 100
                                  ).toFixed(0)}
                                  %)
                                </p>
                              )}
                            </div>
                          )}

                          {/* Keyword Overuse */}
                          {analysis.keywordOveruseResult && (
                            <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                              <p className="font-medium mb-1">
                                Keyword Overuse Detection:
                              </p>
                              <div className="flex items-center mb-1">
                                <span
                                  className={`w-3 h-3 rounded-full mr-2 ${
                                    analysis.keywordOveruseResult.isSuspicious
                                      ? "bg-red-500"
                                      : "bg-green-500"
                                  }`}
                                ></span>
                                <span>
                                  {analysis.keywordOveruseResult.isSuspicious
                                    ? "Suspicious: Overuses generic words"
                                    : "Natural language usage"}
                                </span>
                              </div>
                              {analysis.keywordOveruseResult.suspectWords
                                .length > 0 && (
                                <div className="text-xs ml-5">
                                  <p>
                                    Overuse score:{" "}
                                    {(
                                      analysis.keywordOveruseResult
                                        .overuseScore * 100
                                    ).toFixed(0)}
                                    %
                                  </p>
                                  <p>
                                    Suspect words:{" "}
                                    {analysis.keywordOveruseResult.suspectWords
                                      .map((w) => `${w.word} (${w.count})`)
                                      .join(", ")}
                                  </p>
                                </div>
                              )}
                            </div>
                          )}

                          {/* Sentiment Extremes */}
                          {analysis.sentimentExtremesResult && (
                            <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                              <p className="font-medium mb-1">
                                Sentiment Extremes Detection:
                              </p>
                              <div className="flex items-center mb-1">
                                <span
                                  className={`w-3 h-3 rounded-full mr-2 ${
                                    analysis.sentimentExtremesResult
                                      .isSuspicious
                                      ? "bg-red-500"
                                      : "bg-green-500"
                                  }`}
                                ></span>
                                <span>
                                  {analysis.sentimentExtremesResult.isSuspicious
                                    ? "Suspicious: Extreme sentiment"
                                    : "Balanced sentiment"}
                                </span>
                              </div>
                              <div className="text-xs ml-5">
                                <p>
                                  Sentiment:{" "}
                                  {analysis.sentimentExtremesResult
                                    .sentimentScore > 0
                                    ? `Positive (${(
                                        analysis.sentimentExtremesResult
                                          .sentimentScore * 100
                                      ).toFixed(0)}%)`
                                    : `Negative (${(
                                        Math.abs(
                                          analysis.sentimentExtremesResult
                                            .sentimentScore
                                        ) * 100
                                      ).toFixed(0)}%)`}
                                </p>
                                <p>
                                  Extreme score:{" "}
                                  {(
                                    analysis.sentimentExtremesResult
                                      .extremeScore * 100
                                  ).toFixed(0)}
                                  %
                                </p>
                                {analysis.sentimentExtremesResult.reasons
                                  .length > 0 && (
                                  <ul className="list-disc list-inside">
                                    {analysis.sentimentExtremesResult.reasons.map(
                                      (reason, index) => (
                                        <li key={index}>{reason}</li>
                                      )
                                    )}
                                  </ul>
                                )}
                              </div>
                            </div>
                          )}

                          {/* Cache Comparison */}
                          {analysis.cacheComparisonResult && (
                            <div className="mb-3 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                              <p className="font-medium mb-1">
                                Cache Comparison:
                              </p>
                              <div className="flex items-center mb-1">
                                <span
                                  className={`w-3 h-3 rounded-full mr-2 ${
                                    analysis.cacheComparisonResult
                                      .fakeSimScore >
                                    analysis.cacheComparisonResult.realSimScore
                                      ? "bg-red-500"
                                      : "bg-green-500"
                                  }`}
                                ></span>
                                <span>
                                  {analysis.cacheComparisonResult.fakeSimScore >
                                  analysis.cacheComparisonResult.realSimScore
                                    ? "Suspicious: Similar to known fake reviews"
                                    : "Similar to known real reviews"}
                                </span>
                              </div>
                              <div className="text-xs ml-5">
                                <p>
                                  Similarity to fake reviews:{" "}
                                  {(
                                    analysis.cacheComparisonResult
                                      .fakeSimScore * 100
                                  ).toFixed(0)}
                                  %
                                </p>
                                <p>
                                  Similarity to real reviews:{" "}
                                  {(
                                    analysis.cacheComparisonResult
                                      .realSimScore * 100
                                  ).toFixed(0)}
                                  %
                                </p>
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Summary of reasons */}
                        {reasons.length > 0 && (
                          <div className="mt-1">
                            <p className="font-medium">Key findings:</p>
                            <ul className="list-disc list-inside">
                              {reasons.slice(0, 3).map((reason, index) => (
                                <li key={index}>{reason}</li>
                              ))}
                              {reasons.length > 3 && (
                                <li className="text-gray-500">
                                  ...and {reasons.length - 3} more (see details)
                                </li>
                              )}
                            </ul>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>

            {/* Loading Status and Load More Button */}
            <div className="mt-8 text-center">
              {loadingMoreReviews ? (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg inline-block">
                  <div className="flex items-center">
                    <span className="inline-block h-5 w-5 animate-spin rounded-full border-2 border-solid border-blue-600 border-r-transparent align-[-0.125em] mr-3"></span>
                    <span className="text-blue-800">
                      Loading and analyzing reviews... {allReviews.length} of{" "}
                      {totalReviewCount} loaded
                    </span>
                  </div>
                </div>
              ) : (
                <div className="flex flex-col items-center">
                  <div className="p-4 bg-green-50 border border-green-200 rounded-lg inline-block mb-4">
                    <span className="text-green-800">
                      {allReviews.length === totalReviewCount
                        ? `All ${totalReviewCount} reviews loaded and analyzed`
                        : `${allReviews.length} of ${totalReviewCount} reviews loaded and analyzed`}
                    </span>
                  </div>

                  {hasMoreReviews && (
                    <button
                      onClick={loadAllReviews}
                      className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-lg"
                    >
                      Load More Reviews
                    </button>
                  )}
                </div>
              )}
            </div>

            {/* About ghettoreviews */}
            <div className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 border-2 border-purple-200 rounded-xl text-sm">
              <p className="font-bold text-purple-800 mb-2 text-lg">
                💯 About ghettoreviews 💯
              </p>
              <p className="text-purple-700 mb-2">
                This is a user-generated review site where{" "}
                <strong>real people</strong> share their honest experiences. No
                corporate BS, no paid reviews - just authentic opinions from the
                community.
              </p>
              <p className="text-purple-700">
                <strong>Every review is analyzed</strong> using advanced fake
                detection techniques including repetitive phrasing detection,
                keyword overuse analysis, and sentiment extremes detection to
                help you spot potentially fake reviews.
              </p>
              <p className="text-purple-700 mt-2 font-medium">
                ✨ Add your own review with fun MySpace-style widgets! ✨
              </p>
            </div>
          </>
        ) : (
          <p className="text-gray-500">No reviews available for this place.</p>
        )}
      </div>
    </div>
  );
}

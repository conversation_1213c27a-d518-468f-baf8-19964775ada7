"use client";

import { useState } from "react";
import {
  SplitReview,
  WIDGET_OPTIONS,
  MOOD_OPTIONS,
  VIBE_OPTIONS,
  COMPLAINT_CATEGORIES,
} from "@/utils/userReviews";

interface SplitReviewFormProps {
  placeId: string;
  onSubmit: (review: SplitReview) => void;
  onCancel: () => void;
}

export default function SplitReviewForm({
  placeId,
  onSubmit,
  onCancel,
}: SplitReviewFormProps) {
  const [author, setAuthor] = useState("");
  const [overallRating, setOverallRating] = useState(3);
  const [mood, setMood] = useState("😐");
  const [widget, setWidget] = useState("none");

  // Good stuff
  const [hasGoodStuff, setHasGoodStuff] = useState(true);
  const [goodText, setGoodText] = useState("");
  const [goodVibe, setGoodVibe] = useState("chill");

  // Bad stuff
  const [hasBadStuff, setHasBadStuff] = useState(false);
  const [badText, setBadText] = useState("");
  const [badTitle, setBadTitle] = useState("");
  const [badCategory, setBadCategory] = useState("service");
  const [badSeverity, setBadSeverity] = useState(3);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!author.trim()) {
      alert("Please enter your name!");
      return;
    }

    if (!hasGoodStuff && !hasBadStuff) {
      alert("Please add either good stuff, bad stuff, or both!");
      return;
    }

    if (hasGoodStuff && !goodText.trim()) {
      alert("Please write about the good stuff!");
      return;
    }

    if (hasBadStuff && !badText.trim()) {
      alert("Please write about the bad stuff!");
      return;
    }

    const splitReview: SplitReview = {
      id: Date.now().toString(),
      author: author.trim(),
      timestamp: Date.now(),
      placeId,
      overallRating,
      mood,
      widget: widget !== "none" ? widget : undefined,
      goodStuff: hasGoodStuff
        ? {
            text: goodText.trim(),
            vibe: goodVibe,
            votes: 0,
          }
        : undefined,
      badStuff: hasBadStuff
        ? {
            text: badText.trim(),
            title: badTitle.trim() || undefined,
            category: badCategory,
            severity: badSeverity,
            votes: 0,
          }
        : undefined,
    };

    onSubmit(splitReview);
  };

  return (
    <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl border-2 border-purple-200 dark:border-purple-700 p-6">
      <div className="text-center mb-6">
        <h3 className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
          ✨ Write a Split Review ✨
        </h3>
        <p className="text-gray-600 dark:text-gray-300 text-sm">
          Share both the good and bad in one review! You can write about just the good stuff, just the bad stuff, or both.
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Info */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Your Name
            </label>
            <input
              type="text"
              value={author}
              onChange={(e) => setAuthor(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              placeholder="Enter your name"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Overall Rating
            </label>
            <div className="flex space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => setOverallRating(star)}
                  className={`text-2xl ${
                    star <= overallRating ? "text-yellow-500" : "text-gray-300"
                  }`}
                >
                  ⭐
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Mood and Widget */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Your Mood
            </label>
            <select
              value={mood}
              onChange={(e) => setMood(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            >
              {MOOD_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.value} {option.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              MySpace Widget
            </label>
            <select
              value={widget}
              onChange={(e) => setWidget(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
            >
              {WIDGET_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.emoji} {option.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Review Type Toggles */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="hasGoodStuff"
              checked={hasGoodStuff}
              onChange={(e) => setHasGoodStuff(e.target.checked)}
              className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 dark:focus:ring-green-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="hasGoodStuff" className="text-sm font-medium text-green-700 dark:text-green-300">
              💚 Include Good Stuff
            </label>
          </div>

          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="hasBadStuff"
              checked={hasBadStuff}
              onChange={(e) => setHasBadStuff(e.target.checked)}
              className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500 dark:focus:ring-red-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
            />
            <label htmlFor="hasBadStuff" className="text-sm font-medium text-red-700 dark:text-red-300">
              💩 Include Bad Stuff
            </label>
          </div>
        </div>

        {/* Good Stuff Section */}
        {hasGoodStuff && (
          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200 dark:border-green-700">
            <h4 className="text-lg font-semibold text-green-800 dark:text-green-200 mb-3">
              💚 The Good Stuff
            </h4>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                  What did you love about this place?
                </label>
                <textarea
                  value={goodText}
                  onChange={(e) => setGoodText(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-green-300 dark:border-green-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Tell us about the awesome stuff..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-green-700 dark:text-green-300 mb-2">
                  Vibe
                </label>
                <select
                  value={goodVibe}
                  onChange={(e) => setGoodVibe(e.target.value)}
                  className="w-full px-3 py-2 border border-green-300 dark:border-green-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                >
                  {VIBE_OPTIONS.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Bad Stuff Section */}
        {hasBadStuff && (
          <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 border border-red-200 dark:border-red-700">
            <h4 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-3">
              💩 The Bad Stuff
            </h4>
            
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-red-700 dark:text-red-300 mb-2">
                    Complaint Title (Optional)
                  </label>
                  <input
                    type="text"
                    value={badTitle}
                    onChange={(e) => setBadTitle(e.target.value)}
                    className="w-full px-3 py-2 border border-red-300 dark:border-red-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                    placeholder="Short title for the issue"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-red-700 dark:text-red-300 mb-2">
                    Category
                  </label>
                  <select
                    value={badCategory}
                    onChange={(e) => setBadCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-red-300 dark:border-red-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    {COMPLAINT_CATEGORIES.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.emoji} {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-red-700 dark:text-red-300 mb-2">
                  What went wrong?
                </label>
                <textarea
                  value={badText}
                  onChange={(e) => setBadText(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-red-300 dark:border-red-600 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  placeholder="Tell us about the problems..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-red-700 dark:text-red-300 mb-2">
                  How bad was it? ({badSeverity}/5 💀)
                </label>
                <div className="flex space-x-1">
                  {[1, 2, 3, 4, 5].map((level) => (
                    <button
                      key={level}
                      type="button"
                      onClick={() => setBadSeverity(level)}
                      className={`text-2xl ${
                        level <= badSeverity ? "text-red-500" : "text-gray-300"
                      }`}
                    >
                      💀
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Submit Buttons */}
        <div className="flex space-x-4">
          <button
            type="submit"
            className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-purple-700 hover:to-pink-700 transition-all transform hover:scale-105"
          >
            ✨ Submit Split Review
          </button>
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg font-semibold hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  );
}

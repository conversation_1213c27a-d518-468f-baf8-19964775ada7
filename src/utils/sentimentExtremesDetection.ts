import { PlaceReview } from './googlePlaces';

/**
 * Interface for sentiment extremes detection result
 */
export interface SentimentExtremesResult {
  isSuspicious: boolean;
  sentimentScore: number; // -1 to 1, where -1 is very negative, 1 is very positive
  extremeScore: number; // 0 to 1, higher means more extreme
  reasons: string[];
}

// Positive and negative word lists for basic sentiment analysis
const POSITIVE_WORDS = [
  'good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic',
  'terrific', 'outstanding', 'superb', 'brilliant', 'awesome',
  'delicious', 'delightful', 'enjoyable', 'pleasant', 'satisfied',
  'happy', 'love', 'best', 'perfect', 'recommend', 'favorite',
  'impressed', 'impressive', 'exceptional', 'superior', 'quality'
];

const NEGATIVE_WORDS = [
  'bad', 'terrible', 'awful', 'horrible', 'poor', 'disappointing',
  'disgusting', 'unpleasant', 'mediocre', 'subpar', 'worst',
  'hate', 'dislike', 'avoid', 'never', 'waste', 'regret',
  'unhappy', 'dissatisfied', 'overpriced', 'rude', 'slow',
  'dirty', 'cold', 'bland', 'tasteless', 'unprofessional'
];

// Intensifiers that amplify sentiment
const INTENSIFIERS = [
  'very', 'extremely', 'incredibly', 'absolutely', 'completely',
  'totally', 'utterly', 'really', 'so', 'too', 'highly',
  'exceptionally', 'especially', 'particularly', 'remarkably'
];

/**
 * Calculate sentiment score for a text
 * @param text The text to analyze
 * @returns Object with sentiment score, extreme score, and reasons
 */
function analyzeSentiment(text: string): { 
  sentimentScore: number; 
  extremeScore: number;
  reasons: string[];
} {
  // Normalize text: lowercase and remove punctuation
  const normalizedText = text.toLowerCase().replace(/[^\w\s]/g, '');
  const words = normalizedText.split(/\s+/);
  
  let positiveCount = 0;
  let negativeCount = 0;
  let intensifierCount = 0;
  const reasons: string[] = [];
  
  // Count positive, negative words and intensifiers
  for (const word of words) {
    if (POSITIVE_WORDS.includes(word)) {
      positiveCount++;
    }
    if (NEGATIVE_WORDS.includes(word)) {
      negativeCount++;
    }
    if (INTENSIFIERS.includes(word)) {
      intensifierCount++;
    }
  }
  
  // Calculate basic sentiment score (-1 to 1)
  const totalSentimentWords = positiveCount + negativeCount;
  let sentimentScore = 0;
  
  if (totalSentimentWords > 0) {
    sentimentScore = (positiveCount - negativeCount) / totalSentimentWords;
  }
  
  // Calculate extreme score (0 to 1)
  // Based on: absolute sentiment value, intensifier usage, and review length
  const sentimentIntensity = Math.abs(sentimentScore);
  const intensifierFactor = Math.min(1, intensifierCount / 5); // Cap at 1
  const lengthFactor = Math.max(0, 1 - (words.length / 100)); // Shorter reviews get higher scores
  
  const extremeScore = (sentimentIntensity * 0.5) + (intensifierFactor * 0.3) + (lengthFactor * 0.2);
  
  // Add reasons
  if (sentimentIntensity > 0.7) {
    reasons.push(`Very ${sentimentScore > 0 ? 'positive' : 'negative'} sentiment`);
  }
  
  if (intensifierCount > 2) {
    reasons.push(`Uses ${intensifierCount} intensifiers`);
  }
  
  if (words.length < 20) {
    reasons.push('Very short review');
  }
  
  // Check if review rating matches sentiment
  // (This would require the star rating, which we'll handle in the main function)
  
  return {
    sentimentScore,
    extremeScore,
    reasons
  };
}

/**
 * Detect sentiment extremes in a review
 * @param review The review to analyze
 * @param extremeThreshold Threshold above which a review is considered suspicious (default: 0.6)
 * @returns Analysis result
 */
export function detectSentimentExtremes(
  review: PlaceReview,
  extremeThreshold: number = 0.6
): SentimentExtremesResult {
  const { sentimentScore, extremeScore, reasons } = analyzeSentiment(review.text);
  
  // Check if star rating matches sentiment
  const normalizedRating = (review.rating - 3) / 2; // Convert 1-5 scale to -1 to 1
  const ratingDifference = Math.abs(normalizedRating - sentimentScore);
  
  if (ratingDifference > 0.8) {
    reasons.push('Rating does not match review sentiment');
  }
  
  // Check for extreme ratings (1 or 5 stars)
  if (review.rating === 1 || review.rating === 5) {
    reasons.push(`Extreme ${review.rating}-star rating`);
  }
  
  // Determine if the review is suspicious based on extreme score
  const isSuspicious = extremeScore > extremeThreshold;
  
  return {
    isSuspicious,
    sentimentScore,
    extremeScore,
    reasons
  };
}

/**
 * Analyze all reviews in a place for sentiment extremes
 * @param reviews All reviews for a place
 * @param extremeThreshold Threshold for extremes (default: 0.6)
 * @returns Map of review IDs to analysis results
 */
export function analyzeAllReviewsForSentimentExtremes(
  reviews: PlaceReview[],
  extremeThreshold: number = 0.6
): Map<number, SentimentExtremesResult> {
  const results = new Map<number, SentimentExtremesResult>();
  
  for (const review of reviews) {
    const result = detectSentimentExtremes(review, extremeThreshold);
    results.set(review.time, result);
  }
  
  return results;
}

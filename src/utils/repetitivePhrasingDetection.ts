import { PlaceReview } from './googlePlaces';

/**
 * Interface for repetitive phrasing detection result
 */
export interface RepetitivePhrasingResult {
  isSuspicious: boolean;
  similarityScore: number;
  similarReviews: Array<{
    reviewId: number;
    similarity: number;
  }>;
}

/**
 * Calculate similarity between two strings using Levenshtein distance
 * Returns a value between 0 (completely different) and 1 (identical)
 */
function calculateSimilarity(str1: string, str2: string): number {
  // Normalize strings: lowercase and remove extra spaces
  const s1 = str1.toLowerCase().replace(/\s+/g, ' ').trim();
  const s2 = str2.toLowerCase().replace(/\s+/g, ' ').trim();
  
  // If either string is empty, return 0
  if (s1.length === 0 || s2.length === 0) return 0;
  
  // If strings are identical, return 1
  if (s1 === s2) return 1;
  
  // Calculate Levenshtein distance
  const matrix: number[][] = [];
  
  // Initialize matrix
  for (let i = 0; i <= s1.length; i++) {
    matrix[i] = [i];
  }
  
  for (let j = 0; j <= s2.length; j++) {
    matrix[0][j] = j;
  }
  
  // Fill matrix
  for (let i = 1; i <= s1.length; i++) {
    for (let j = 1; j <= s2.length; j++) {
      const cost = s1.charAt(i - 1) === s2.charAt(j - 1) ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,      // deletion
        matrix[i][j - 1] + 1,      // insertion
        matrix[i - 1][j - 1] + cost // substitution
      );
    }
  }
  
  // Calculate similarity as 1 - normalized distance
  const distance = matrix[s1.length][s2.length];
  const maxLength = Math.max(s1.length, s2.length);
  
  return 1 - distance / maxLength;
}

/**
 * Detect repetitive phrasing in a review compared to other reviews
 * @param review The review to analyze
 * @param allReviews All reviews for comparison
 * @param similarityThreshold Threshold above which reviews are considered similar (default: 0.8)
 * @returns Analysis result
 */
export function detectRepetitivePhrasing(
  review: PlaceReview,
  allReviews: PlaceReview[],
  similarityThreshold: number = 0.8
): RepetitivePhrasingResult {
  const similarReviews: Array<{ reviewId: number; similarity: number }> = [];
  
  // Compare with all other reviews
  for (const otherReview of allReviews) {
    // Skip comparing with itself
    if (otherReview.time === review.time) continue;
    
    const similarity = calculateSimilarity(review.text, otherReview.text);
    
    // If similarity is above threshold, add to similar reviews
    if (similarity > similarityThreshold) {
      similarReviews.push({
        reviewId: otherReview.time,
        similarity
      });
    }
  }
  
  // Calculate average similarity score
  const averageSimilarity = similarReviews.length > 0
    ? similarReviews.reduce((sum, item) => sum + item.similarity, 0) / similarReviews.length
    : 0;
  
  // Determine if the review is suspicious based on similar reviews
  const isSuspicious = similarReviews.length > 0;
  
  return {
    isSuspicious,
    similarityScore: averageSimilarity,
    similarReviews
  };
}

/**
 * Analyze all reviews in a place for repetitive phrasing
 * @param reviews All reviews for a place
 * @param similarityThreshold Threshold for similarity (default: 0.8)
 * @returns Map of review IDs to analysis results
 */
export function analyzeAllReviewsForRepetitivePhrasing(
  reviews: PlaceReview[],
  similarityThreshold: number = 0.8
): Map<number, RepetitivePhrasingResult> {
  const results = new Map<number, RepetitivePhrasingResult>();
  
  for (const review of reviews) {
    const result = detectRepetitivePhrasing(review, reviews, similarityThreshold);
    results.set(review.time, result);
  }
  
  return results;
}

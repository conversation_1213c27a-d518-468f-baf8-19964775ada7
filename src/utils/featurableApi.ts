/**
 * Utility functions for working with the Featurable API
 * This API provides more Google reviews than the standard Google Places API
 */

// Types for Featurable API responses
export interface FeaturableReview {
  reviewId: string;
  reviewer: {
    displayName: string;
    profilePhotoUrl?: string;
    isAnonymous: boolean;
  };
  reviewReply?: {
    comment: string;
    updateTime: string;
  };
  createTime: string;
  updateTime: string;
  comment: string;
  starRating?: number; // Adding this as it might be in the response
  rating?: number;     // Alternative field name for star rating
}

export interface FeaturableReviewsResponse {
  success: boolean;
  reviews: FeaturableReview[];
  totalReviewCount: number;
  averageRating: number;
  nextPageToken?: string;
}

/**
 * Convert Featurable reviews to a format compatible with our application
 */
export function convertFeaturableReviews(featurableReviews: FeaturableReview[]): any[] {
  return featurableReviews.map(review => {
    // Parse the create time to get a timestamp
    const createTime = new Date(review.createTime).getTime();
    
    // Calculate relative time description
    const now = new Date();
    const reviewDate = new Date(review.createTime);
    const diffMs = now.getTime() - reviewDate.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    let relativeTime = '';
    if (diffDays < 1) {
      relativeTime = 'today';
    } else if (diffDays < 2) {
      relativeTime = 'yesterday';
    } else if (diffDays < 7) {
      relativeTime = `${diffDays} days ago`;
    } else if (diffDays < 30) {
      const weeks = Math.floor(diffDays / 7);
      relativeTime = `${weeks} ${weeks === 1 ? 'week' : 'weeks'} ago`;
    } else if (diffDays < 365) {
      const months = Math.floor(diffDays / 30);
      relativeTime = `${months} ${months === 1 ? 'month' : 'months'} ago`;
    } else {
      const years = Math.floor(diffDays / 365);
      relativeTime = `${years} ${years === 1 ? 'year' : 'years'} ago`;
    }
    
    // Get the star rating (might be in different fields)
    const rating = review.starRating || review.rating || 5; // Default to 5 if not provided
    
    return {
      author_name: review.reviewer.displayName,
      rating: rating,
      relative_time_description: relativeTime,
      text: review.comment,
      time: createTime,
      profile_photo_url: review.reviewer.profilePhotoUrl,
      reviewId: review.reviewId
    };
  });
}

/**
 * Fetch reviews from the Featurable API
 */
export async function fetchFeaturableReviews(
  accountId: string,
  locationId: string,
  nextPageToken?: string,
  pageSize: number = 50,
  minStars: number = 1
): Promise<{
  reviews: any[];
  totalReviewCount: number;
  averageRating: number;
  nextPageToken?: string;
}> {
  try {
    // Build the URL with query parameters
    let url = `/api/featurable/reviews?accountId=${encodeURIComponent(accountId)}&locationId=${encodeURIComponent(locationId)}`;
    
    // Add optional parameters if provided
    if (nextPageToken) {
      url += `&nextPageToken=${encodeURIComponent(nextPageToken)}`;
    }
    
    url += `&pageSize=${pageSize}&minStars=${minStars}`;
    
    console.log(`Fetching reviews from Featurable API: ${url}`);
    
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`Error fetching reviews: ${response.statusText}`);
    }
    
    const data: FeaturableReviewsResponse = await response.json();
    console.log(`Received ${data.reviews?.length || 0} reviews from Featurable API`);
    
    // Convert the reviews to our application format
    const convertedReviews = convertFeaturableReviews(data.reviews || []);
    
    return {
      reviews: convertedReviews,
      totalReviewCount: data.totalReviewCount || 0,
      averageRating: data.averageRating || 0,
      nextPageToken: data.nextPageToken
    };
  } catch (error) {
    console.error("Error fetching reviews from Featurable API:", error);
    
    // Return empty results instead of throwing to make the process more resilient
    return {
      reviews: [],
      totalReviewCount: 0,
      averageRating: 0
    };
  }
}

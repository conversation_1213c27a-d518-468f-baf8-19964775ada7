import { PlaceReview } from "./googlePlaces";
import {
  detectRepetitivePhrasing,
  RepetitivePhrasingResult,
} from "./repetitivePhrasingDetection";
import {
  detectKeywordOveruse,
  KeywordOveruseResult,
} from "./keywordOveruseDetection";
import {
  detectSentimentExtremes,
  SentimentExtremesResult,
} from "./sentimentExtremesDetection";
import {
  compareReviewWithCache,
  getCachedPlace,
  updateReviewCache,
} from "./reviewCache";

// Interface for review analysis result
export interface ReviewAnalysis {
  isFake: boolean;
  confidence: number; // 0 to 1
  reasons: string[];

  // Results from individual techniques
  basicAnalysis: {
    isFake: boolean;
    confidence: number;
    reasons: string[];
  };
  repetitivePhrasingResult: RepetitivePhrasingResult | null;
  keywordOveruseResult: KeywordOveruseResult | null;
  sentimentExtremesResult: SentimentExtremesResult | null;
  cacheComparisonResult: {
    fakeSimScore: number;
    realSimScore: number;
  } | null;
}

/**
 * Basic heuristic analysis of a review
 */
function basicAnalyzeReview(review: PlaceReview): {
  isFake: boolean;
  confidence: number;
  reasons: string[];
} {
  const reasons: string[] = [];
  let fakeScore = 0;
  let realScore = 0;

  // Check review length
  if (review.text.length < 20) {
    reasons.push("Very short review");
    fakeScore += 0.3;
  } else if (review.text.length > 500) {
    reasons.push("Detailed, lengthy review");
    realScore += 0.2;
  }

  // Check for extreme ratings (1 or 5 stars)
  if (review.rating === 1 || review.rating === 5) {
    // Extreme ratings are more common in fake reviews
    reasons.push("Extreme rating (1 or 5 stars)");
    fakeScore += 0.1;
  } else {
    // Moderate ratings are more likely in genuine reviews
    reasons.push("Moderate rating");
    realScore += 0.2;
  }

  // Check for specific details in the review
  const hasSpecificDetails =
    /specific|particular|detail|exactly|precisely/i.test(review.text);
  if (hasSpecificDetails) {
    reasons.push("Contains specific details");
    realScore += 0.3;
  }

  // Check for mention of staff, service, or specific items
  const mentionsSpecifics =
    /staff|service|waiter|waitress|manager|dish|menu|item|ordered/i.test(
      review.text
    );
  if (mentionsSpecifics) {
    reasons.push("Mentions specific aspects (staff, dishes, etc.)");
    realScore += 0.3;
  }

  // Check for emotional language
  const emotionalLanguage =
    /amazing|awesome|terrible|worst|best|ever|absolutely|definitely|extremely/i.test(
      review.text
    );
  if (emotionalLanguage) {
    reasons.push("Uses highly emotional language");
    fakeScore += 0.2;
  }

  // Check for personal experience markers
  const personalExperience = /I|we|my|our|me|us/i.test(review.text);
  if (personalExperience) {
    reasons.push("Written from personal perspective");
    realScore += 0.2;
  }

  // Check for time-specific details
  const timeSpecific =
    /yesterday|last week|month|year|date|day|time|hour|morning|evening|afternoon|night/i.test(
      review.text
    );
  if (timeSpecific) {
    reasons.push("Includes time-specific details");
    realScore += 0.2;
  }

  // Calculate final scores
  const totalScore = fakeScore + realScore;
  const fakeConfidence = totalScore > 0 ? fakeScore / totalScore : 0.5;

  // Determine if the review is fake based on confidence threshold
  const isFake = fakeConfidence > 0.5;
  const confidence = isFake ? fakeConfidence : 1 - fakeConfidence;

  return {
    isFake,
    confidence,
    reasons,
  };
}

/**
 * Analyzes a review to determine if it's likely fake or real using multiple techniques
 *
 * @param review The review to analyze
 * @param allReviews All reviews for the place (for comparative analysis)
 * @param placeId The place ID (for cache comparison)
 */
export async function analyzeReview(
  review: PlaceReview,
  allReviews: PlaceReview[] = [],
  placeId: string = ""
): Promise<ReviewAnalysis> {
  // Run basic analysis
  const basicResult = basicAnalyzeReview(review);

  // Initialize results from other techniques
  let repetitiveResult: RepetitivePhrasingResult | null = null;
  let keywordResult: KeywordOveruseResult | null = null;
  let sentimentResult: SentimentExtremesResult | null = null;
  let cacheResult: { fakeSimScore: number; realSimScore: number } | null = null;

  // Run repetitive phrasing detection if we have other reviews to compare with
  if (allReviews.length > 0) {
    repetitiveResult = detectRepetitivePhrasing(review, allReviews);
  }

  // Run keyword overuse detection
  keywordResult = detectKeywordOveruse(review);

  // Run sentiment extremes detection
  sentimentResult = detectSentimentExtremes(review);

  // Compare with cache if place ID is provided
  if (placeId) {
    cacheResult = compareReviewWithCache(review, placeId);
  }

  // Combine results from all techniques to make a final determination
  const allReasons: string[] = [...basicResult.reasons];
  let totalFakeScore = basicResult.isFake ? 1 : 0;
  let techniqueCount = 1; // Start with 1 for basic analysis

  // Add repetitive phrasing results
  if (repetitiveResult) {
    techniqueCount++;
    if (repetitiveResult.isSuspicious) {
      totalFakeScore++;
      allReasons.push(
        `Similar to ${repetitiveResult.similarReviews.length} other review(s)`
      );
    }
  }

  // Add keyword overuse results
  if (keywordResult) {
    techniqueCount++;
    if (keywordResult.isSuspicious) {
      totalFakeScore++;
      allReasons.push(
        `Overuses generic positive/negative words (${keywordResult.suspectWords.length} found)`
      );
    }
  }

  // Add sentiment extremes results
  if (sentimentResult) {
    techniqueCount++;
    if (sentimentResult.isSuspicious) {
      totalFakeScore++;
      sentimentResult.reasons.forEach((reason) => allReasons.push(reason));
    }
  }

  // Add cache comparison results
  if (cacheResult) {
    techniqueCount++;
    if (cacheResult.fakeSimScore > cacheResult.realSimScore) {
      totalFakeScore++;
      allReasons.push(`More similar to known fake reviews than real ones`);
    }
  }

  // Calculate final fake probability
  const fakeProbability = totalFakeScore / techniqueCount;
  const isFake = fakeProbability >= 0.5;
  const confidence = Math.abs((fakeProbability - 0.5) * 2); // Scale to 0-1

  return {
    isFake,
    confidence,
    reasons: allReasons,
    basicAnalysis: basicResult,
    repetitivePhrasingResult: repetitiveResult,
    keywordOveruseResult: keywordResult,
    sentimentExtremesResult: sentimentResult,
    cacheComparisonResult: cacheResult,
  };
}

/**
 * Analyze all reviews for a place and update the cache
 */
export async function analyzeAllReviews(
  reviews: PlaceReview[],
  placeId: string
): Promise<Map<number, ReviewAnalysis>> {
  const results = new Map<number, ReviewAnalysis>();

  // Analyze each review
  for (const review of reviews) {
    const result = await analyzeReview(review, reviews, placeId);
    results.set(review.time, result);
  }

  // Skip cache update for now
  console.log(
    `Analysis complete for ${reviews.length} reviews. Cache update skipped.`
  );

  return results;
}

/**
 * This is a placeholder for future Gemini API integration
 * We'll implement this later when we're ready to use the Gemini API
 */
export async function analyzeReviewWithGemini(
  review: PlaceReview,
  allReviews: PlaceReview[] = [],
  placeId: string = ""
): Promise<ReviewAnalysis> {
  // This will be implemented later
  // For now, just use the combined analysis
  return analyzeReview(review, allReviews, placeId);
}

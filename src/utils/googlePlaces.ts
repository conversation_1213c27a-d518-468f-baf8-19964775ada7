// Google Places API utility functions

// Types for Places API responses
export interface PlaceSearchResult {
  place_id: string;
  name: string;
  formatted_address?: string;
  rating?: number;
  user_ratings_total?: number;
  photos?: Array<{
    photo_reference: string;
    height: number;
    width: number;
  }>;
  vicinity?: string;
  types?: string[];
}

export interface PlaceDetails {
  place_id: string;
  name: string;
  formatted_address: string;
  formatted_phone_number?: string;
  website?: string;
  rating?: number;
  user_ratings_total?: number;
  reviews?: PlaceReview[];
  photos?: Array<{
    photo_reference: string;
    height: number;
    width: number;
  }>;
  opening_hours?: {
    weekday_text: string[];
  };
}

export interface PlaceReview {
  author_name: string;
  rating: number;
  relative_time_description: string;
  text: string;
  time: number;
  profile_photo_url?: string;
}

// Function to search for places
export async function searchPlaces(
  query: string
): Promise<PlaceSearchResult[]> {
  try {
    const response = await fetch(
      `/api/places/search?query=${encodeURIComponent(query)}`
    );

    if (!response.ok) {
      throw new Error(`Error searching places: ${response.statusText}`);
    }

    const data = await response.json();
    return data.results;
  } catch (error) {
    console.error("Error searching places:", error);
    throw error;
  }
}

// Function to get place details including reviews
export async function getPlaceDetails(placeId: string): Promise<PlaceDetails> {
  try {
    const response = await fetch(
      `/api/places/details?placeId=${encodeURIComponent(placeId)}`
    );

    if (!response.ok) {
      throw new Error(`Error fetching place details: ${response.statusText}`);
    }

    const data = await response.json();
    return data.result;
  } catch (error) {
    console.error("Error fetching place details:", error);
    throw error;
  }
}

// Function to get a photo URL from a photo reference
export function getPhotoUrl(
  photoReference: string,
  maxWidth: number = 400
): string {
  return `/api/places/photo?photoReference=${encodeURIComponent(
    photoReference
  )}&maxWidth=${maxWidth}`;
}

// Function to get reviews from the Google Places API
export async function getMoreReviews(placeId: string): Promise<{
  reviews: PlaceReview[];
  totalReviews: number;
  hasMore: boolean;
}> {
  try {
    console.log(`Fetching reviews for ${placeId} from Google Places API`);

    const url = `/api/places/more-reviews?placeId=${encodeURIComponent(
      placeId
    )}`;
    console.log(`Request URL: ${url}`);

    const response = await fetch(url);

    if (!response.ok) {
      throw new Error(`Error fetching reviews: ${response.statusText}`);
    }

    const data = await response.json();
    console.log(`Received data from API:`, data);

    // Ensure we have the expected properties, with fallbacks
    const reviews = Array.isArray(data.reviews) ? data.reviews : [];
    const totalReviews =
      typeof data.total_reviews === "number" ? data.total_reviews : 0;
    const hasMore = Boolean(data.has_more);

    console.log(`Received ${reviews.length} reviews from Google Places API`);

    return {
      reviews,
      totalReviews,
      hasMore,
    };
  } catch (error) {
    console.error("Error fetching reviews:", error);

    // Return empty results instead of throwing to make the process more resilient
    return {
      reviews: [],
      totalReviews: 0,
      hasMore: false,
    };
  }
}

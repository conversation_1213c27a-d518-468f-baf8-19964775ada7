import { PlaceReview } from './googlePlaces';

/**
 * Interface for cached review data
 */
export interface CachedReviewData {
  placeId: string;
  lastUpdated: string; // ISO date string
  reviews: PlaceReview[];
  analysisResults: {
    fakeReviews: number[]; // Array of review IDs (time) that are likely fake
    realReviews: number[]; // Array of review IDs (time) that are likely real
    fakeReviewsText: string; // Concatenated text of fake reviews for comparison
    realReviewsText: string; // Concatenated text of real reviews for comparison
  };
}

/**
 * Interface for the entire cache
 */
export interface ReviewCache {
  version: string;
  places: Record<string, CachedReviewData>;
}

// Initial empty cache
const EMPTY_CACHE: ReviewCache = {
  version: '1.0',
  places: {}
};

/**
 * Save the review cache to localStorage
 * @param cache The cache to save
 */
export function saveReviewCache(cache: ReviewCache): void {
  try {
    localStorage.setItem('reviewAnalyzerCache', JSON.stringify(cache));
  } catch (error) {
    console.error('Error saving review cache:', error);
  }
}

/**
 * Load the review cache from localStorage
 * @returns The loaded cache or an empty cache if none exists
 */
export function loadReviewCache(): ReviewCache {
  try {
    const cachedData = localStorage.getItem('reviewAnalyzerCache');
    if (!cachedData) return EMPTY_CACHE;
    
    return JSON.parse(cachedData) as ReviewCache;
  } catch (error) {
    console.error('Error loading review cache:', error);
    return EMPTY_CACHE;
  }
}

/**
 * Get cached data for a specific place
 * @param placeId The place ID to get data for
 * @returns The cached data or null if not found
 */
export function getCachedPlace(placeId: string): CachedReviewData | null {
  const cache = loadReviewCache();
  return cache.places[placeId] || null;
}

/**
 * Update the cache with new review data for a place
 * @param placeId The place ID
 * @param reviews The reviews for the place
 * @param fakeReviewIds Array of review IDs that are likely fake
 * @param realReviewIds Array of review IDs that are likely real
 */
export function updateReviewCache(
  placeId: string,
  reviews: PlaceReview[],
  fakeReviewIds: number[],
  realReviewIds: number[]
): void {
  const cache = loadReviewCache();
  
  // Create concatenated text for fake and real reviews
  const fakeReviewsText = reviews
    .filter(review => fakeReviewIds.includes(review.time))
    .map(review => review.text)
    .join(' ');
  
  const realReviewsText = reviews
    .filter(review => realReviewIds.includes(review.time))
    .map(review => review.text)
    .join(' ');
  
  // Update cache for this place
  cache.places[placeId] = {
    placeId,
    lastUpdated: new Date().toISOString(),
    reviews,
    analysisResults: {
      fakeReviews: fakeReviewIds,
      realReviews: realReviewIds,
      fakeReviewsText,
      realReviewsText
    }
  };
  
  // Save updated cache
  saveReviewCache(cache);
}

/**
 * Compare a new review against cached fake and real reviews
 * @param review The review to compare
 * @param placeId The place ID
 * @returns Similarity scores or null if no cache exists
 */
export function compareReviewWithCache(
  review: PlaceReview,
  placeId: string
): { fakeSimScore: number; realSimScore: number } | null {
  const cachedPlace = getCachedPlace(placeId);
  if (!cachedPlace) return null;
  
  // Simple word overlap similarity calculation
  function calculateWordOverlap(text1: string, text2: string): number {
    if (!text1 || !text2) return 0;
    
    const words1 = new Set(text1.toLowerCase().split(/\s+/));
    const words2 = new Set(text2.toLowerCase().split(/\s+/));
    
    // Count words that appear in both sets
    let overlapCount = 0;
    for (const word of words1) {
      if (words2.has(word)) {
        overlapCount++;
      }
    }
    
    // Calculate Jaccard similarity: intersection size / union size
    const unionSize = words1.size + words2.size - overlapCount;
    return unionSize > 0 ? overlapCount / unionSize : 0;
  }
  
  const fakeSimScore = calculateWordOverlap(review.text, cachedPlace.analysisResults.fakeReviewsText);
  const realSimScore = calculateWordOverlap(review.text, cachedPlace.analysisResults.realReviewsText);
  
  return { fakeSimScore, realSimScore };
}

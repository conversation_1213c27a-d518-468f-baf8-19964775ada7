/**
 * Utility functions for managing user-submitted reviews
 */

export interface UserReview {
  id: string;
  author: string;
  rating: number;
  text: string;
  timestamp: number;
  widget?: string; // For fun MySpace-style widgets
  mood?: string; // User's mood when writing the review
  vibe?: string; // Overall vibe rating
  placeId: string; // Associate with place
}

/**
 * Save a user review to localStorage
 */
export function saveUserReview(review: UserReview): void {
  try {
    const existingReviews = getUserReviews(review.placeId);
    const updatedReviews = [review, ...existingReviews];
    
    localStorage.setItem(
      `userReviews_${review.placeId}`, 
      JSON.stringify(updatedReviews)
    );
  } catch (error) {
    console.error('Error saving user review:', error);
  }
}

/**
 * Get all user reviews for a specific place
 */
export function getUserReviews(placeId: string): UserReview[] {
  try {
    const reviews = localStorage.getItem(`userReviews_${placeId}`);
    return reviews ? JSON.parse(reviews) : [];
  } catch (error) {
    console.error('Error loading user reviews:', error);
    return [];
  }
}

/**
 * Get all user reviews across all places
 */
export function getAllUserReviews(): UserReview[] {
  try {
    const allReviews: UserReview[] = [];
    
    // Iterate through localStorage to find all user review keys
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith('userReviews_')) {
        const reviews = localStorage.getItem(key);
        if (reviews) {
          allReviews.push(...JSON.parse(reviews));
        }
      }
    }
    
    // Sort by timestamp (newest first)
    return allReviews.sort((a, b) => b.timestamp - a.timestamp);
  } catch (error) {
    console.error('Error loading all user reviews:', error);
    return [];
  }
}

/**
 * Delete a user review
 */
export function deleteUserReview(placeId: string, reviewId: string): void {
  try {
    const reviews = getUserReviews(placeId);
    const filteredReviews = reviews.filter(review => review.id !== reviewId);
    
    localStorage.setItem(
      `userReviews_${placeId}`, 
      JSON.stringify(filteredReviews)
    );
  } catch (error) {
    console.error('Error deleting user review:', error);
  }
}

/**
 * Widget options for MySpace-style reviews
 */
export const WIDGET_OPTIONS = [
  { value: 'none', label: '✨ No Widget', emoji: '' },
  { value: 'glitter', label: '✨ Glitter', emoji: '✨' },
  { value: 'hearts', label: '💖 Hearts', emoji: '💖' },
  { value: 'fire', label: '🔥 Fire', emoji: '🔥' },
  { value: 'music', label: '🎵 Music Notes', emoji: '🎵' },
  { value: 'rainbow', label: '🌈 Rainbow', emoji: '🌈' },
  { value: 'skull', label: '💀 Skull', emoji: '💀' },
  { value: 'star', label: '⭐ Star', emoji: '⭐' },
];

/**
 * Mood options for reviews
 */
export const MOOD_OPTIONS = [
  { value: '😐', label: 'Neutral' },
  { value: '😊', label: 'Happy' },
  { value: '😍', label: 'Love it' },
  { value: '😤', label: 'Frustrated' },
  { value: '😭', label: 'Disappointed' },
  { value: '🤔', label: 'Confused' },
  { value: '😎', label: 'Cool' },
  { value: '🤩', label: 'Amazed' },
];

/**
 * Vibe options for reviews
 */
export const VIBE_OPTIONS = [
  { value: 'chill', label: 'Chill' },
  { value: 'hype', label: 'Hype' },
  { value: 'cozy', label: 'Cozy' },
  { value: 'bougie', label: 'Bougie' },
  { value: 'sketchy', label: 'Sketchy' },
  { value: 'basic', label: 'Basic' },
  { value: 'fire', label: 'Fire' },
  { value: 'mid', label: 'Mid' },
];

/**
 * Utility functions for managing user-submitted reviews
 */

export interface UserReview {
  id: string;
  author: string;
  rating: number;
  text: string;
  timestamp: number;
  widget?: string; // For fun MySpace-style widgets
  mood?: string; // User's mood when writing the review
  vibe?: string; // Overall vibe rating
  placeId: string; // Associate with place
  votes: number; // Vote count for this review
}

export interface UserComplaint {
  id: string;
  author: string;
  title: string; // Short title for the complaint
  text: string; // Detailed complaint
  category: string; // Type of complaint
  timestamp: number;
  placeId: string; // Associate with place
  severity: number; // 1-5 scale of how bad it was
  votes: number; // Vote count for this complaint
}

/**
 * New Split Review interface - combines good and bad in one entry
 */
export interface SplitReview {
  id: string;
  author: string;
  timestamp: number;
  placeId: string;

  // Overall rating and mood
  overallRating: number; // 1-5 stars overall
  mood?: string;
  widget?: string;

  // Good stuff (optional)
  goodStuff?: {
    text: string;
    vibe?: string;
    votes: number;
  };

  // Bad stuff (optional)
  badStuff?: {
    text: string;
    title?: string; // Short complaint title
    category?: string;
    severity?: number; // 1-5 scale
    votes: number;
  };
}

export interface Vote {
  id: string; // review, complaint, or split review ID
  type: "review" | "complaint" | "splitReview";
  subType?: "good" | "bad"; // For split reviews, which part was voted on
  placeId: string;
  voterFingerprint: string; // Simple fingerprint to prevent duplicate votes
}

/**
 * Save a user review to localStorage
 */
export function saveUserReview(review: UserReview): void {
  try {
    const existingReviews = getUserReviews(review.placeId);
    const updatedReviews = [review, ...existingReviews];

    localStorage.setItem(
      `userReviews_${review.placeId}`,
      JSON.stringify(updatedReviews)
    );
  } catch (error) {
    console.error("Error saving user review:", error);
  }
}

/**
 * Get all user reviews for a specific place
 */
export function getUserReviews(placeId: string): UserReview[] {
  try {
    const reviews = localStorage.getItem(`userReviews_${placeId}`);
    return reviews ? JSON.parse(reviews) : [];
  } catch (error) {
    console.error("Error loading user reviews:", error);
    return [];
  }
}

/**
 * Get all user reviews across all places
 */
export function getAllUserReviews(): UserReview[] {
  try {
    const allReviews: UserReview[] = [];

    // Iterate through localStorage to find all user review keys
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith("userReviews_")) {
        const reviews = localStorage.getItem(key);
        if (reviews) {
          allReviews.push(...JSON.parse(reviews));
        }
      }
    }

    // Sort by timestamp (newest first)
    return allReviews.sort((a, b) => b.timestamp - a.timestamp);
  } catch (error) {
    console.error("Error loading all user reviews:", error);
    return [];
  }
}

/**
 * Delete a user review
 */
export function deleteUserReview(placeId: string, reviewId: string): void {
  try {
    const reviews = getUserReviews(placeId);
    const filteredReviews = reviews.filter((review) => review.id !== reviewId);

    localStorage.setItem(
      `userReviews_${placeId}`,
      JSON.stringify(filteredReviews)
    );
  } catch (error) {
    console.error("Error deleting user review:", error);
  }
}

/**
 * Save a user complaint to localStorage
 */
export function saveUserComplaint(complaint: UserComplaint): void {
  try {
    const existingComplaints = getUserComplaints(complaint.placeId);
    const updatedComplaints = [complaint, ...existingComplaints];

    localStorage.setItem(
      `userComplaints_${complaint.placeId}`,
      JSON.stringify(updatedComplaints)
    );
  } catch (error) {
    console.error("Error saving user complaint:", error);
  }
}

/**
 * Get all user complaints for a specific place
 */
export function getUserComplaints(placeId: string): UserComplaint[] {
  try {
    const complaints = localStorage.getItem(`userComplaints_${placeId}`);
    return complaints ? JSON.parse(complaints) : [];
  } catch (error) {
    console.error("Error loading user complaints:", error);
    return [];
  }
}

/**
 * Delete a user complaint
 */
export function deleteUserComplaint(
  placeId: string,
  complaintId: string
): void {
  try {
    const complaints = getUserComplaints(placeId);
    const filteredComplaints = complaints.filter(
      (complaint) => complaint.id !== complaintId
    );

    localStorage.setItem(
      `userComplaints_${placeId}`,
      JSON.stringify(filteredComplaints)
    );
  } catch (error) {
    console.error("Error deleting user complaint:", error);
  }
}

/**
 * Save a split review to localStorage
 */
export function saveSplitReview(splitReview: SplitReview): void {
  try {
    const existingSplitReviews = getSplitReviews(splitReview.placeId);
    const updatedSplitReviews = [splitReview, ...existingSplitReviews];

    localStorage.setItem(
      `splitReviews_${splitReview.placeId}`,
      JSON.stringify(updatedSplitReviews)
    );
  } catch (error) {
    console.error("Error saving split review:", error);
  }
}

/**
 * Get all split reviews for a specific place
 */
export function getSplitReviews(placeId: string): SplitReview[] {
  try {
    const splitReviews = localStorage.getItem(`splitReviews_${placeId}`);
    return splitReviews ? JSON.parse(splitReviews) : [];
  } catch (error) {
    console.error("Error loading split reviews:", error);
    return [];
  }
}

/**
 * Delete a split review
 */
export function deleteSplitReview(placeId: string, reviewId: string): void {
  try {
    const splitReviews = getSplitReviews(placeId);
    const filteredReviews = splitReviews.filter(
      (review) => review.id !== reviewId
    );

    localStorage.setItem(
      `splitReviews_${placeId}`,
      JSON.stringify(filteredReviews)
    );
  } catch (error) {
    console.error("Error deleting split review:", error);
  }
}

/**
 * Generate a simple fingerprint for voting (to prevent duplicate votes)
 */
function generateVoterFingerprint(): string {
  // Simple fingerprint based on browser characteristics
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");
  ctx!.textBaseline = "top";
  ctx!.font = "14px Arial";
  ctx!.fillText("Fingerprint", 2, 2);

  const fingerprint = [
    navigator.userAgent,
    navigator.language,
    screen.width + "x" + screen.height,
    new Date().getTimezoneOffset(),
    canvas.toDataURL(),
  ].join("|");

  // Simple hash
  let hash = 0;
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return hash.toString();
}

/**
 * Get user votes for a place
 */
function getUserVotes(placeId: string): Vote[] {
  try {
    const votes = localStorage.getItem(`userVotes_${placeId}`);
    return votes ? JSON.parse(votes) : [];
  } catch (error) {
    console.error("Error loading user votes:", error);
    return [];
  }
}

/**
 * Save a vote
 */
function saveVote(vote: Vote): void {
  try {
    const existingVotes = getUserVotes(vote.placeId);
    const updatedVotes = [vote, ...existingVotes];

    localStorage.setItem(
      `userVotes_${vote.placeId}`,
      JSON.stringify(updatedVotes)
    );
  } catch (error) {
    console.error("Error saving vote:", error);
  }
}

/**
 * Check if user has already voted for an item
 */
export function hasUserVoted(
  placeId: string,
  itemId: string,
  type: "review" | "complaint" | "splitReview",
  subType?: "good" | "bad"
): boolean {
  try {
    const fingerprint = generateVoterFingerprint();
    const votes = getUserVotes(placeId);
    return votes.some(
      (vote) =>
        vote.id === itemId &&
        vote.type === type &&
        vote.voterFingerprint === fingerprint &&
        (subType ? vote.subType === subType : true)
    );
  } catch (error) {
    console.error("Error checking vote status:", error);
    return false;
  }
}

/**
 * Vote for a review
 */
export function voteForReview(placeId: string, reviewId: string): boolean {
  try {
    const fingerprint = generateVoterFingerprint();

    // Check if already voted
    if (hasUserVoted(placeId, reviewId, "review")) {
      return false;
    }

    // Save the vote
    const vote: Vote = {
      id: reviewId,
      type: "review",
      placeId: placeId,
      voterFingerprint: fingerprint,
    };
    saveVote(vote);

    // Update the review vote count
    const reviews = getUserReviews(placeId);
    const updatedReviews = reviews.map((review) =>
      review.id === reviewId ? { ...review, votes: review.votes + 1 } : review
    );

    localStorage.setItem(
      `userReviews_${placeId}`,
      JSON.stringify(updatedReviews)
    );

    return true;
  } catch (error) {
    console.error("Error voting for review:", error);
    return false;
  }
}

/**
 * Vote for a complaint
 */
export function voteForComplaint(
  placeId: string,
  complaintId: string
): boolean {
  try {
    const fingerprint = generateVoterFingerprint();

    // Check if already voted
    if (hasUserVoted(placeId, complaintId, "complaint")) {
      return false;
    }

    // Save the vote
    const vote: Vote = {
      id: complaintId,
      type: "complaint",
      placeId: placeId,
      voterFingerprint: fingerprint,
    };
    saveVote(vote);

    // Update the complaint vote count
    const complaints = getUserComplaints(placeId);
    const updatedComplaints = complaints.map((complaint) =>
      complaint.id === complaintId
        ? { ...complaint, votes: complaint.votes + 1 }
        : complaint
    );

    localStorage.setItem(
      `userComplaints_${placeId}`,
      JSON.stringify(updatedComplaints)
    );

    return true;
  } catch (error) {
    console.error("Error voting for complaint:", error);
    return false;
  }
}

/**
 * Vote for a split review (good or bad part)
 */
export function voteForSplitReview(
  placeId: string,
  reviewId: string,
  subType: "good" | "bad"
): boolean {
  try {
    const fingerprint = generateVoterFingerprint();

    // Check if already voted for this specific part
    if (hasUserVoted(placeId, reviewId, "splitReview", subType)) {
      return false;
    }

    // Save the vote
    const vote: Vote = {
      id: reviewId,
      type: "splitReview",
      subType: subType,
      placeId: placeId,
      voterFingerprint: fingerprint,
    };
    saveVote(vote);

    // Update the split review vote count
    const splitReviews = getSplitReviews(placeId);
    const updatedSplitReviews = splitReviews.map((review) => {
      if (review.id === reviewId) {
        if (subType === "good" && review.goodStuff) {
          return {
            ...review,
            goodStuff: {
              ...review.goodStuff,
              votes: review.goodStuff.votes + 1,
            },
          };
        } else if (subType === "bad" && review.badStuff) {
          return {
            ...review,
            badStuff: {
              ...review.badStuff,
              votes: review.badStuff.votes + 1,
            },
          };
        }
      }
      return review;
    });

    localStorage.setItem(
      `splitReviews_${placeId}`,
      JSON.stringify(updatedSplitReviews)
    );

    return true;
  } catch (error) {
    console.error("Error voting for split review:", error);
    return false;
  }
}

/**
 * Get top voted reviews for summary bubbles
 */
export function getTopReviews(
  placeId: string,
  limit: number = 3
): UserReview[] {
  try {
    const reviews = getUserReviews(placeId);
    return reviews
      .filter((review) => review.votes > 0) // Only show reviews with votes
      .sort((a, b) => b.votes - a.votes) // Sort by votes descending
      .slice(0, limit);
  } catch (error) {
    console.error("Error getting top reviews:", error);
    return [];
  }
}

/**
 * Get top voted complaints for summary bubbles
 */
export function getTopComplaints(
  placeId: string,
  limit: number = 3
): UserComplaint[] {
  try {
    const complaints = getUserComplaints(placeId);
    return complaints
      .filter((complaint) => complaint.votes > 0) // Only show complaints with votes
      .sort((a, b) => b.votes - a.votes) // Sort by votes descending
      .slice(0, limit);
  } catch (error) {
    console.error("Error getting top complaints:", error);
    return [];
  }
}

/**
 * Get top voted good stuff from split reviews
 */
export function getTopSplitGoodStuff(
  placeId: string,
  limit: number = 3
): Array<{
  review: SplitReview;
  goodStuff: NonNullable<SplitReview["goodStuff"]>;
}> {
  try {
    const splitReviews = getSplitReviews(placeId);
    return splitReviews
      .filter((review) => review.goodStuff && review.goodStuff.votes > 0)
      .map((review) => ({ review, goodStuff: review.goodStuff! }))
      .sort((a, b) => b.goodStuff.votes - a.goodStuff.votes)
      .slice(0, limit);
  } catch (error) {
    console.error("Error getting top split good stuff:", error);
    return [];
  }
}

/**
 * Get top voted bad stuff from split reviews
 */
export function getTopSplitBadStuff(
  placeId: string,
  limit: number = 3
): Array<{
  review: SplitReview;
  badStuff: NonNullable<SplitReview["badStuff"]>;
}> {
  try {
    const splitReviews = getSplitReviews(placeId);
    return splitReviews
      .filter((review) => review.badStuff && review.badStuff.votes > 0)
      .map((review) => ({ review, badStuff: review.badStuff! }))
      .sort((a, b) => b.badStuff.votes - a.badStuff.votes)
      .slice(0, limit);
  } catch (error) {
    console.error("Error getting top split bad stuff:", error);
    return [];
  }
}

/**
 * Widget options for MySpace-style reviews
 */
export const WIDGET_OPTIONS = [
  { value: "none", label: "✨ No Widget", emoji: "" },
  { value: "glitter", label: "✨ Glitter", emoji: "✨" },
  { value: "hearts", label: "💖 Hearts", emoji: "💖" },
  { value: "fire", label: "🔥 Fire", emoji: "🔥" },
  { value: "music", label: "🎵 Music Notes", emoji: "🎵" },
  { value: "rainbow", label: "🌈 Rainbow", emoji: "🌈" },
  { value: "skull", label: "💀 Skull", emoji: "💀" },
  { value: "star", label: "⭐ Star", emoji: "⭐" },
];

/**
 * Mood options for reviews
 */
export const MOOD_OPTIONS = [
  { value: "😐", label: "Neutral" },
  { value: "😊", label: "Happy" },
  { value: "😍", label: "Love it" },
  { value: "😤", label: "Frustrated" },
  { value: "😭", label: "Disappointed" },
  { value: "🤔", label: "Confused" },
  { value: "😎", label: "Cool" },
  { value: "🤩", label: "Amazed" },
];

/**
 * Vibe options for reviews
 */
export const VIBE_OPTIONS = [
  { value: "chill", label: "Chill" },
  { value: "hype", label: "Hype" },
  { value: "cozy", label: "Cozy" },
  { value: "bougie", label: "Bougie" },
  { value: "sketchy", label: "Sketchy" },
  { value: "basic", label: "Basic" },
  { value: "fire", label: "Fire" },
  { value: "mid", label: "Mid" },
];

/**
 * Complaint categories for the Shit List
 */
export const COMPLAINT_CATEGORIES = [
  { value: "service", label: "🤬 Terrible Service", emoji: "🤬" },
  { value: "food", label: "🤮 Disgusting Food", emoji: "🤮" },
  { value: "cleanliness", label: "🦠 Dirty AF", emoji: "🦠" },
  { value: "pricing", label: "💸 Overpriced Garbage", emoji: "💸" },
  { value: "staff", label: "😡 Rude Staff", emoji: "😡" },
  { value: "atmosphere", label: "💀 Dead Vibes", emoji: "💀" },
  { value: "wait", label: "⏰ Forever Wait", emoji: "⏰" },
  { value: "scam", label: "🚨 Total Scam", emoji: "🚨" },
  { value: "other", label: "💩 Other BS", emoji: "💩" },
];

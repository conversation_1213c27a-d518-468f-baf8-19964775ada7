/**
 * Utility functions for managing user-submitted reviews
 */

export interface UserReview {
  id: string;
  author: string;
  rating: number;
  text: string;
  timestamp: number;
  widget?: string; // For fun MySpace-style widgets
  mood?: string; // User's mood when writing the review
  vibe?: string; // Overall vibe rating
  placeId: string; // Associate with place
}

export interface UserComplaint {
  id: string;
  author: string;
  title: string; // Short title for the complaint
  text: string; // Detailed complaint
  category: string; // Type of complaint
  timestamp: number;
  placeId: string; // Associate with place
  severity: number; // 1-5 scale of how bad it was
}

/**
 * Save a user review to localStorage
 */
export function saveUserReview(review: UserReview): void {
  try {
    const existingReviews = getUserReviews(review.placeId);
    const updatedReviews = [review, ...existingReviews];

    localStorage.setItem(
      `userReviews_${review.placeId}`,
      JSON.stringify(updatedReviews)
    );
  } catch (error) {
    console.error("Error saving user review:", error);
  }
}

/**
 * Get all user reviews for a specific place
 */
export function getUserReviews(placeId: string): UserReview[] {
  try {
    const reviews = localStorage.getItem(`userReviews_${placeId}`);
    return reviews ? JSON.parse(reviews) : [];
  } catch (error) {
    console.error("Error loading user reviews:", error);
    return [];
  }
}

/**
 * Get all user reviews across all places
 */
export function getAllUserReviews(): UserReview[] {
  try {
    const allReviews: UserReview[] = [];

    // Iterate through localStorage to find all user review keys
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && key.startsWith("userReviews_")) {
        const reviews = localStorage.getItem(key);
        if (reviews) {
          allReviews.push(...JSON.parse(reviews));
        }
      }
    }

    // Sort by timestamp (newest first)
    return allReviews.sort((a, b) => b.timestamp - a.timestamp);
  } catch (error) {
    console.error("Error loading all user reviews:", error);
    return [];
  }
}

/**
 * Delete a user review
 */
export function deleteUserReview(placeId: string, reviewId: string): void {
  try {
    const reviews = getUserReviews(placeId);
    const filteredReviews = reviews.filter((review) => review.id !== reviewId);

    localStorage.setItem(
      `userReviews_${placeId}`,
      JSON.stringify(filteredReviews)
    );
  } catch (error) {
    console.error("Error deleting user review:", error);
  }
}

/**
 * Save a user complaint to localStorage
 */
export function saveUserComplaint(complaint: UserComplaint): void {
  try {
    const existingComplaints = getUserComplaints(complaint.placeId);
    const updatedComplaints = [complaint, ...existingComplaints];

    localStorage.setItem(
      `userComplaints_${complaint.placeId}`,
      JSON.stringify(updatedComplaints)
    );
  } catch (error) {
    console.error("Error saving user complaint:", error);
  }
}

/**
 * Get all user complaints for a specific place
 */
export function getUserComplaints(placeId: string): UserComplaint[] {
  try {
    const complaints = localStorage.getItem(`userComplaints_${placeId}`);
    return complaints ? JSON.parse(complaints) : [];
  } catch (error) {
    console.error("Error loading user complaints:", error);
    return [];
  }
}

/**
 * Delete a user complaint
 */
export function deleteUserComplaint(
  placeId: string,
  complaintId: string
): void {
  try {
    const complaints = getUserComplaints(placeId);
    const filteredComplaints = complaints.filter(
      (complaint) => complaint.id !== complaintId
    );

    localStorage.setItem(
      `userComplaints_${placeId}`,
      JSON.stringify(filteredComplaints)
    );
  } catch (error) {
    console.error("Error deleting user complaint:", error);
  }
}

/**
 * Widget options for MySpace-style reviews
 */
export const WIDGET_OPTIONS = [
  { value: "none", label: "✨ No Widget", emoji: "" },
  { value: "glitter", label: "✨ Glitter", emoji: "✨" },
  { value: "hearts", label: "💖 Hearts", emoji: "💖" },
  { value: "fire", label: "🔥 Fire", emoji: "🔥" },
  { value: "music", label: "🎵 Music Notes", emoji: "🎵" },
  { value: "rainbow", label: "🌈 Rainbow", emoji: "🌈" },
  { value: "skull", label: "💀 Skull", emoji: "💀" },
  { value: "star", label: "⭐ Star", emoji: "⭐" },
];

/**
 * Mood options for reviews
 */
export const MOOD_OPTIONS = [
  { value: "😐", label: "Neutral" },
  { value: "😊", label: "Happy" },
  { value: "😍", label: "Love it" },
  { value: "😤", label: "Frustrated" },
  { value: "😭", label: "Disappointed" },
  { value: "🤔", label: "Confused" },
  { value: "😎", label: "Cool" },
  { value: "🤩", label: "Amazed" },
];

/**
 * Vibe options for reviews
 */
export const VIBE_OPTIONS = [
  { value: "chill", label: "Chill" },
  { value: "hype", label: "Hype" },
  { value: "cozy", label: "Cozy" },
  { value: "bougie", label: "Bougie" },
  { value: "sketchy", label: "Sketchy" },
  { value: "basic", label: "Basic" },
  { value: "fire", label: "Fire" },
  { value: "mid", label: "Mid" },
];

/**
 * Complaint categories for the Shit List
 */
export const COMPLAINT_CATEGORIES = [
  { value: "service", label: "🤬 Terrible Service", emoji: "🤬" },
  { value: "food", label: "🤮 Disgusting Food", emoji: "🤮" },
  { value: "cleanliness", label: "🦠 Dirty AF", emoji: "🦠" },
  { value: "pricing", label: "💸 Overpriced Garbage", emoji: "💸" },
  { value: "staff", label: "😡 Rude Staff", emoji: "😡" },
  { value: "atmosphere", label: "💀 Dead Vibes", emoji: "💀" },
  { value: "wait", label: "⏰ Forever Wait", emoji: "⏰" },
  { value: "scam", label: "🚨 Total Scam", emoji: "🚨" },
  { value: "other", label: "💩 Other BS", emoji: "💩" },
];

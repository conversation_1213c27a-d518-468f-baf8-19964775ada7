import { PlaceReview } from './googlePlaces';

/**
 * Interface for keyword overuse detection result
 */
export interface KeywordOveruseResult {
  isSuspicious: boolean;
  overuseScore: number; // 0 to 1, higher means more suspicious
  suspectWords: Array<{
    word: string;
    count: number;
  }>;
}

// List of generic, overly positive words commonly found in fake reviews
const SUSPECT_KEYWORDS = [
  'amazing', 'awesome', 'best', 'excellent', 'fantastic', 
  'great', 'incredible', 'love', 'perfect', 'recommend',
  'wonderful', 'delicious', 'definitely', 'absolutely',
  'ever', 'always', 'never', 'every', 'all', 'very',
  'super', 'really', 'totally', 'completely', 'honestly',
  'literally', 'actually', 'simply', 'just', 'so',
  'must', 'highly', 'extremely', 'beyond', 'unbelievable'
];

/**
 * Calculate the density of suspect keywords in a text
 * @param text The text to analyze
 * @returns Object with overuse score and suspect words found
 */
function calculateKeywordDensity(text: string): { 
  overuseScore: number; 
  suspectWords: Array<{ word: string; count: number }> 
} {
  // Normalize text: lowercase and remove punctuation
  const normalizedText = text.toLowerCase().replace(/[^\w\s]/g, '');
  const words = normalizedText.split(/\s+/);
  
  // Count occurrences of suspect keywords
  const suspectWordCounts = new Map<string, number>();
  
  for (const word of words) {
    if (SUSPECT_KEYWORDS.includes(word)) {
      const currentCount = suspectWordCounts.get(word) || 0;
      suspectWordCounts.set(word, currentCount + 1);
    }
  }
  
  // Convert to array for easier handling
  const suspectWords = Array.from(suspectWordCounts.entries()).map(([word, count]) => ({
    word,
    count
  }));
  
  // Calculate overuse score
  // Formula: (total suspect words) / (total words) * adjustment factor
  const totalSuspectWords = suspectWords.reduce((sum, item) => sum + item.count, 0);
  const totalWords = words.length;
  
  // Adjustment factor increases score for shorter reviews with many suspect words
  const adjustmentFactor = Math.min(1.5, 1 + (10 / Math.max(totalWords, 10)));
  
  // Calculate score (0 to 1)
  let overuseScore = (totalSuspectWords / Math.max(totalWords, 1)) * adjustmentFactor;
  overuseScore = Math.min(1, overuseScore); // Cap at 1
  
  return {
    overuseScore,
    suspectWords
  };
}

/**
 * Detect keyword overuse in a review
 * @param review The review to analyze
 * @param overuseThreshold Threshold above which a review is considered suspicious (default: 0.2)
 * @returns Analysis result
 */
export function detectKeywordOveruse(
  review: PlaceReview,
  overuseThreshold: number = 0.2
): KeywordOveruseResult {
  const { overuseScore, suspectWords } = calculateKeywordDensity(review.text);
  
  // Determine if the review is suspicious based on overuse score
  const isSuspicious = overuseScore > overuseThreshold;
  
  return {
    isSuspicious,
    overuseScore,
    suspectWords
  };
}

/**
 * Analyze all reviews in a place for keyword overuse
 * @param reviews All reviews for a place
 * @param overuseThreshold Threshold for overuse (default: 0.2)
 * @returns Map of review IDs to analysis results
 */
export function analyzeAllReviewsForKeywordOveruse(
  reviews: PlaceReview[],
  overuseThreshold: number = 0.2
): Map<number, KeywordOveruseResult> {
  const results = new Map<number, KeywordOveruseResult>();
  
  for (const review of reviews) {
    const result = detectKeywordOveruse(review, overuseThreshold);
    results.set(review.time, result);
  }
  
  return results;
}

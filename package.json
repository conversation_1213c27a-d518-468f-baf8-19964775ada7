{"name": "ghettoreviews", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "next": "14.2.5"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "tailwindcss": "^3.4.0", "postcss": "^8.4.0", "autoprefixer": "^10.4.0", "eslint": "^8.57.0", "eslint-config-next": "14.2.5"}}